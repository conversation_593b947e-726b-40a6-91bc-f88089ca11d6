/**
 * Test file for tool tag parser functions
 */

import { parseToolTag, formatToolCall, processToolTags, processToolTagsWithCollapse } from './toolTagParser';

// Test data
const sampleToolContent = `{"name": "mark_task_complete", "arguments": {"summary": "Provided a comprehensive summary of the latest news regarding Thailand and Cambodia, focusing on recent border clashes, casualties, displacement, ceasefire agreements, and ongoing diplomatic efforts. Information was gathered from multiple credible news sources including Al Jazeera, Euronews, CBS News, and Anadolu Agency."}, "final_message": "The latest situation between Thailand and Cambodia involves serious border conflicts, with casualties and mass displacement, but an immediate and unconditional ceasefire has been agreed upon following international mediation. Diplomatic talks are ongoing to ensure lasting peace. Let me know if you'd like updates on humanitarian efforts or historical context."}`;

const sampleMessageWithToolTag = `Thailand and Cambodia have recently experienced intense border clashes, resulting in significant casualties and displacement.

<tool call>${sampleToolContent}</tool call>

Task completed. The latest developments between Thailand and Cambodia have been summarized.`;

// Test functions
console.log('=== Testing parseToolTag ===');
const parsed = parseToolTag(sampleToolContent);
console.log('Parsed tool call:', parsed);

console.log('\n=== Testing formatToolCall ===');
if (parsed) {
  const formatted = formatToolCall(parsed);
  console.log('Formatted tool call:\n', formatted);
}

console.log('\n=== Testing processToolTags ===');
const processed = processToolTags(sampleMessageWithToolTag);
console.log('Processed message:\n', processed);

console.log('\n=== Testing processToolTagsWithCollapse ===');
const processedCollapse = processToolTagsWithCollapse(sampleMessageWithToolTag);
console.log('Processed message with collapse:\n', processedCollapse);

// Test edge cases
console.log('\n=== Testing edge cases ===');

// Invalid JSON
const invalidJson = `{"name": "test", "arguments": {invalid json}`;
console.log('Invalid JSON result:', parseToolTag(invalidJson));

// Empty content
console.log('Empty content result:', processToolTags(''));

// No tool tags
const noToolTags = 'This is a regular message without any tool tags.';
console.log('No tool tags result:', processToolTags(noToolTags));

// Multiple tool tags
const multipleToolTags = `First message.

<tool>{"name": "tool1", "arguments": {"param": "value1"}}</tool>

Middle content.

<tool>{"name": "tool2", "arguments": {"param": "value2"}, "summary": "Second tool"}</tool>

Final message.`;

console.log('Multiple tool tags result:\n', processToolTags(multipleToolTags));
