import { NextRequest, NextResponse } from 'next/server';
import { APIService } from '@/lib/api';
import { discoverServiceUrl, clearServiceUrlCache } from '@/lib/service-discovery';
import fs from 'fs';
import path from 'path';

interface EnhancedChatRequest {
  messages: Array<{
    role: string;
    content: string;
  }>;
  mode?: 'single' | 'orchestrator';
  stream?: boolean;
  config?: {
    max_iterations?: number;
    parallel_agents?: number;
    timeout?: number;
    tools_enabled?: boolean;
  };
}

// Function to get user settings from file
function getUserSettings() {
  try {
    const settingsPath = path.join(process.cwd(), 'user-settings.json');
    if (fs.existsSync(settingsPath)) {
      const settingsData = fs.readFileSync(settingsPath, 'utf8');
      return JSON.parse(settingsData);
    }
  } catch (error) {
    console.warn('Failed to load user settings:', error);
  }
  return null;
}

export async function POST(request: NextRequest) {
  let messages: any[] | undefined;
  let mode: 'single' | 'orchestrator' = 'single';
  let stream: boolean = true;
  let config: any = {};
  
  try {
    const body: EnhancedChatRequest = await request.json();
    ({ messages, mode = 'single', stream = true, config = {} } = body);

    console.log(`Enhanced chat request: mode=${mode}, messages=${messages?.length || 0}, stream=${stream}, tools_enabled=${config.tools_enabled}`);

    // Validate request
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json({ error: 'Invalid messages format' }, { status: 400 });
    }

    // Discover the correct service URL
    const PYTHON_SERVICE_URL = await discoverServiceUrl();
    
    // Determine endpoint based on mode
    const endpoint = mode === 'orchestrator' ? '/chat/orchestrator' : '/chat/single';
    const serviceUrl = `${PYTHON_SERVICE_URL}${endpoint}`;

    // Check if Python service is available (it should be since we just discovered it)
    let serviceAvailable = false;
    try {
      console.log(`Checking Python service health at: ${PYTHON_SERVICE_URL}/health`);
      const healthCheck = await fetch(`${PYTHON_SERVICE_URL}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });

      serviceAvailable = healthCheck.ok;
      console.log(`Python service health check: ${serviceAvailable ? 'OK' : 'FAILED'}`);
      
      // If health check fails, clear the cached URL to force rediscovery next time
      if (!serviceAvailable) {
        clearServiceUrlCache();
      }
    } catch (error) {
      console.warn('Python service health check failed:', error);
      serviceAvailable = false;
      // Clear cached URL to force rediscovery next time
      clearServiceUrlCache();
    }

    // If service is not available, check if tools are explicitly enabled
    if (!serviceAvailable) {
      if (mode === 'orchestrator') {
        return NextResponse.json({ 
          error: 'Enhanced orchestrator mode is currently unavailable. Please try again later or use simple chat mode.' 
        }, { status: 503 });
      }
      
      // If tools are explicitly enabled, don't fallback - force Python API usage
       if (config.tools_enabled === true) {
         console.log('🔧 TOOL FORCING: Python service unavailable but tools explicitly enabled - blocking fallback');
         return NextResponse.json({ 
           error: 'Tools functionality requires the Python service which is currently unavailable. Please try again later or disable tools to use simple chat mode.' 
         }, { status: 503 });
       }
      
      console.log('Python service unavailable, falling back to direct Chutes API (tools disabled)');
      return fallbackToDirectAPI(messages, stream);
    }

    // Get API key from environment
    const apiKey = process.env.NEXT_PUBLIC_CHUTES_API_TOKEN;
    
    // Get user settings and determine the model to use
    const userSettings = getUserSettings();
    let selectedModel = null;
    
    if (userSettings && userSettings.preferredModels) {
      // Map mode to the appropriate model setting
      if (mode === 'orchestrator') {
        selectedModel = userSettings.preferredModels.orchestrator;
      } else {
        selectedModel = userSettings.preferredModels.enhanced;
      }
    }
    
    console.log(`Using model: ${selectedModel || 'default'} for mode: ${mode}`);
    
    // Forward request to Python service
    console.log(`Forwarding request to Python service: ${serviceUrl}`);
    // Create a custom AbortController for dynamic timeout management
    const abortController = new AbortController();
    let timeoutId: NodeJS.Timeout;
    
    // Function to reset timeout on progress updates
    const resetTimeout = () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        console.log('Request timeout after no progress updates');
        abortController.abort();
      }, 300000); // 5 minutes timeout, reset on each progress update
    };
    
    // Start initial timeout
    resetTimeout();

    const response = await fetch(serviceUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages,
        mode,
        stream,
        config,
        apiKey, // Include API key in request
        model: selectedModel // Include selected model from user settings
      }),
      signal: abortController.signal
    });

    if (!response.ok) {
      console.error(`Python service error: ${response.status} ${response.statusText}`);
      
      // Try fallback for single mode
      if (mode === 'single') {
        console.log('Python service failed, falling back to direct API');
        return fallbackToDirectAPI(messages, stream);
      }
      
      throw new Error(`Python service error: ${response.status}`);
    }

    if (stream && response.body) {
      // Stream response back to client with dynamic timeout reset on progress updates
      try {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        const readableStream = new ReadableStream({
          start(controller) {
            function pump(): Promise<void> {
              return reader.read().then(({ done, value }) => {
                if (done) {
                  // Clear timeout when stream is done
                  if (timeoutId) clearTimeout(timeoutId);
                  controller.close();
                  return;
                }
                
                // Decode chunk and reset timeout on any data received
                const chunk = decoder.decode(value, { stream: true });
                
                // Reset timeout on any data received (indicates connection is alive)
                resetTimeout();
                
                // Log progress updates for debugging
                if (chunk.includes('"progress"')) {
                  console.log('Progress update detected');
                }
                
                controller.enqueue(value);
                return pump();
              }).catch(error => {
                // Clear timeout on error
                if (timeoutId) clearTimeout(timeoutId);
                controller.error(error);
              });
            }
            return pump();
          },
          cancel() {
            // Clear timeout if stream is cancelled
            if (timeoutId) clearTimeout(timeoutId);
            reader.releaseLock();
          }
        });
        
        return new Response(readableStream, {
          headers: {
            'Content-Type': 'text/plain',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no', // Disable nginx buffering
          },
        });
      } catch (streamError) {
        // Clear timeout on error
        if (timeoutId) clearTimeout(timeoutId);
        console.error('Error streaming response:', streamError);
        throw new Error('Failed to stream response from Python service');
      }
    } else {
      // Clear timeout for non-streaming responses
      if (timeoutId) clearTimeout(timeoutId);
      const data = await response.json();
      return NextResponse.json(data);
    }

  } catch (error) {
    console.error('Enhanced chat API error:', error);
    
    // Clear timeout on error
    if (typeof timeoutId !== 'undefined' && timeoutId) {
      clearTimeout(timeoutId);
    }
    
    // Try fallback for single mode if we have the request data
    try {
      if (messages && mode !== 'orchestrator') {
        console.log('Error occurred, attempting fallback to direct API');
        return fallbackToDirectAPI(messages, stream);
      }
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError);
    }
    
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

async function fallbackToDirectAPI(messages: any[], stream: boolean) {
  console.log('Executing fallback to direct Chutes API');
  
  // Get user settings to determine the model to use and token limits
  const userSettings = getUserSettings();
  let selectedModel = null;
  let maxTokens = 8192; // Default value

  if (userSettings && userSettings.preferredModels) {
    // Use simple mode model for fallback
    selectedModel = userSettings.preferredModels.simple;
  }

  if (userSettings && userSettings.responseSettings) {
    // Use appropriate token limit based on streaming mode
    maxTokens = stream
      ? userSettings.responseSettings.streaming?.simple || 8192
      : userSettings.responseSettings.maxTokens?.simple || 8192;
  }

  console.log(`Fallback using model: ${selectedModel || 'default'}, maxTokens: ${maxTokens}`);
  
  try {
    if (stream) {
      // Implement streaming fallback
      const encoder = new TextEncoder();
      const readable = new ReadableStream({
        async start(controller) {
          try {
            await APIService.sendMessage(
              messages,
              (chunk: string) => {
                const chunkData = {
                  choices: [{
                    delta: { content: chunk },
                    finish_reason: null
                  }]
                };
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunkData)}\n\n`));
              },
              () => {
                const finalChunk = {
                  choices: [{
                    delta: {},
                    finish_reason: 'stop'
                  }]
                };
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
                controller.enqueue(encoder.encode('data: [DONE]\n\n'));
                controller.close();
              },
              (error: string) => {
                const errorData = { error: { message: error } };
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`));
                controller.close();
              },
              selectedModel // Pass the selected model from user settings
            );
          } catch (error) {
            console.error('Fallback streaming error:', error);
            controller.error(error);
          }
        }
      });

      return new Response(readable, {
        headers: {
          'Content-Type': 'text/plain',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    } else {
      const response = await APIService.sendNonStreamingMessage(messages, selectedModel);
      return NextResponse.json({ response });
    }
  } catch (error) {
    console.error('Fallback API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Fallback API failed' },
      { status: 500 }
    );
  }
}

// Health check endpoint for the enhanced chat service
export async function GET() {
  try {
    // Discover the correct service URL
    const PYTHON_SERVICE_URL = await discoverServiceUrl();
    
    // Check Python service status
    const healthCheck = await fetch(`${PYTHON_SERVICE_URL}/health`, {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });

    const pythonServiceStatus = healthCheck.ok;
    
    return NextResponse.json({
      status: 'healthy',
      python_service: pythonServiceStatus ? 'available' : 'unavailable',
      python_service_url: PYTHON_SERVICE_URL,
      fallback: 'available',
      modes: {
        single: pythonServiceStatus ? 'enhanced' : 'fallback',
        orchestrator: pythonServiceStatus ? 'available' : 'unavailable'
      }
    });
  } catch (error) {
    return NextResponse.json({
      status: 'partial',
      python_service: 'unavailable',
      fallback: 'available',
      modes: {
        single: 'fallback',
        orchestrator: 'unavailable'
      },
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
