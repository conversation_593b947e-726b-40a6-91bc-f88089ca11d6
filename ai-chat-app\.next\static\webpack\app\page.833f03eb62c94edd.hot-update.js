"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIService: () => (/* binding */ APIService)\n/* harmony export */ });\nconst API_URL = 'https://llm.chutes.ai/v1/chat/completions';\nconst DEFAULT_MODEL = 'Qwen/Qwen3-235B-A22B-Instruct-2507';\nconst MAX_RETRIES = 3;\nconst RETRY_DELAY = 5000; // 5 seconds\nclass APIService {\n    static async sleep(ms) {\n        return new Promise((resolve)=>setTimeout(resolve, ms));\n    }\n    static async retryWithBackoff(operation) {\n        let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : MAX_RETRIES, baseDelay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : RETRY_DELAY;\n        let lastError;\n        for(let attempt = 0; attempt <= maxRetries; attempt++){\n            try {\n                return await operation();\n            } catch (error) {\n                lastError = error;\n                // Check if it's a 503 error (service overload)\n                if (error instanceof Error && error.message.includes('503')) {\n                    if (attempt < maxRetries) {\n                        const delay = baseDelay * Math.pow(2, attempt); // Exponential backoff\n                        console.log(\"Attempt \".concat(attempt + 1, \" failed with 503 error. Retrying in \").concat(delay, \"ms...\"));\n                        await this.sleep(delay);\n                        continue;\n                    }\n                }\n                // For non-503 errors or final attempt, throw immediately\n                throw error;\n            }\n        }\n        throw lastError;\n    }\n    static getApiKey() {\n        // In a real app, this should be from environment variables\n        // For now, we'll expect it to be set in the browser's environment\n        return \"cpk_be0507bf8b1c4e8e92323cc5b59b6d6f.a6aa7553cf3857ee8a6d07b5ceb6b6c6.GqbvdwyFEK53nS3xfB6YVke4HdD5RA5G\" || 0;\n    }\n    static async sendMessage(messages, onChunk, onComplete, onError, model, maxTokens) {\n        const apiKey = this.getApiKey();\n        if (!apiKey) {\n            const error = 'API key not found. Please set NEXT_PUBLIC_CHUTES_API_TOKEN environment variable.';\n            console.error(error);\n            onError === null || onError === void 0 ? void 0 : onError(error);\n            throw new Error(error);\n        }\n        const request = {\n            model: model || DEFAULT_MODEL,\n            messages,\n            stream: true,\n            max_tokens: maxTokens || 8192,\n            temperature: 0.7\n        };\n        console.log('Sending API request:', {\n            url: API_URL,\n            model: model || DEFAULT_MODEL,\n            messageCount: messages.length\n        });\n        try {\n            const response = await this.retryWithBackoff(async ()=>{\n                const response = await fetch(API_URL, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': \"Bearer \".concat(apiKey),\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(request)\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    const error = \"API request failed: \".concat(response.status, \" \").concat(response.statusText, \" - \").concat(errorText);\n                    throw new Error(error);\n                }\n                return response;\n            });\n            console.log('API response status:', response.status, response.statusText);\n            if (!response.body) {\n                const error = 'No response body received';\n                console.error(error);\n                onError === null || onError === void 0 ? void 0 : onError(error);\n                throw new Error(error);\n            }\n            console.log('Starting stream processing...');\n            return await this.processStreamResponse(response.body, onChunk, onComplete, onError);\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n            console.error('API request error:', error);\n            onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n            throw error;\n        }\n    }\n    static async processStreamResponse(body, onChunk, onComplete, onError) {\n        const reader = body.getReader();\n        const decoder = new TextDecoder();\n        let fullContent = '';\n        let buffer = '';\n        try {\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) {\n                    console.log('Stream completed, full content:', fullContent);\n                    onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n                    break;\n                }\n                // Decode the chunk and add to buffer\n                const chunk = decoder.decode(value, {\n                    stream: true\n                });\n                buffer += chunk;\n                // Process complete lines (split by double newlines for SSE format)\n                const parts = buffer.split('\\n\\n');\n                buffer = parts.pop() || ''; // Keep incomplete part in buffer\n                for (const part of parts){\n                    const lines = part.split('\\n');\n                    for (const line of lines){\n                        const trimmedLine = line.trim();\n                        if (trimmedLine.startsWith('data: ')) {\n                            const data = trimmedLine.slice(6).trim();\n                            if (data === '[DONE]') {\n                                console.log('Received [DONE], completing stream');\n                                onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n                                return fullContent;\n                            }\n                            if (data === '' || data === '{}') {\n                                continue; // Skip empty data lines\n                            }\n                            try {\n                                var _parsed_choices__delta, _parsed_choices_, _parsed_choices_1;\n                                const parsed = JSON.parse(data);\n                                const content = (_parsed_choices_ = parsed.choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content;\n                                if (content) {\n                                    fullContent += content;\n                                    onChunk === null || onChunk === void 0 ? void 0 : onChunk(content);\n                                }\n                                // Check for finish_reason\n                                if (((_parsed_choices_1 = parsed.choices[0]) === null || _parsed_choices_1 === void 0 ? void 0 : _parsed_choices_1.finish_reason) === 'stop') {\n                                    console.log('Received stop signal');\n                                    onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n                                    return fullContent;\n                                }\n                            } catch (parseError) {\n                                // Skip invalid JSON chunks\n                                console.warn('Failed to parse chunk:', data, parseError);\n                            }\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Stream processing error';\n            console.error('API Stream processing error:', error);\n            console.error('API: Calling onError callback with:', errorMessage);\n            onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n            throw error;\n        } finally{\n            reader.releaseLock();\n        }\n        return fullContent;\n    }\n    static async sendNonStreamingMessage(messages, model) {\n        const apiKey = this.getApiKey();\n        if (!apiKey) {\n            throw new Error('API key not found. Please set NEXT_PUBLIC_CHUTES_API_TOKEN environment variable.');\n        }\n        const request = {\n            model: model || DEFAULT_MODEL,\n            messages,\n            stream: false,\n            max_tokens: 4000,\n            temperature: 0.7\n        };\n        console.log('Sending non-streaming API request:', {\n            url: API_URL,\n            model: model || DEFAULT_MODEL,\n            messageCount: messages.length\n        });\n        try {\n            var _data_choices__message, _data_choices_;\n            const response = await this.retryWithBackoff(async ()=>{\n                const response = await fetch(API_URL, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': \"Bearer \".concat(apiKey),\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(request)\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(\"API request failed: \".concat(response.status, \" \").concat(response.statusText, \" - \").concat(errorText));\n                }\n                return response;\n            });\n            const data = await response.json();\n            return ((_data_choices_ = data.choices[0]) === null || _data_choices_ === void 0 ? void 0 : (_data_choices__message = _data_choices_.message) === null || _data_choices__message === void 0 ? void 0 : _data_choices__message.content) || '';\n        } catch (error) {\n            console.error('Non-streaming API request failed:', error);\n            throw error;\n        }\n    }\n    static async refineText(text, model) {\n        const refinePrompt = 'Please refine and improve the following text to make it clearer, more precise, and better suited for AI interaction. Keep the original intent but enhance clarity, grammar, and structure. Return ONLY the refined text without any additional commentary or explanation:\\n\\n\"'.concat(text, '\"\\n\\nRefined version:');\n        const messages = [\n            {\n                role: 'user',\n                content: refinePrompt\n            }\n        ];\n        const refinedText = await APIService.sendNonStreamingMessage(messages, model);\n        // Remove leading/trailing quotes and newlines\n        return refinedText.replace(/^[\\n\\r\"]+|[\\n\\r\"]+$/g, '').trim();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});