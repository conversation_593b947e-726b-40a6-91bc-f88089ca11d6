# PowerShell script to create a Windows shortcut for Nexus AI Chat App

# Get the current directory
$currentDir = (Get-Location).Path

# Define paths
$targetPath = Join-Path $currentDir "start-nexus.bat"
$shortcutPath = [Environment]::GetFolderPath("Desktop") + "\Nexus AI Chat.lnk"
$startMenuPath = [Environment]::GetFolderPath("StartMenu") + "\Programs\Nexus AI Chat.lnk"

# Create shortcut object
$WshShell = New-Object -comObject WScript.Shell

# Create desktop shortcut
$Shortcut = $WshShell.CreateShortcut($shortcutPath)
$Shortcut.TargetPath = $targetPath
$Shortcut.WorkingDirectory = $currentDir
$Shortcut.Description = "Start Nexus AI Chat Application"
$Shortcut.IconLocation = "shell32.dll,25"  # Chat bubble icon
$Shortcut.Save()

# Create start menu shortcut
$StartMenuShortcut = $WshShell.CreateShortcut($startMenuPath)
$StartMenuShortcut.TargetPath = $targetPath
$StartMenuShortcut.WorkingDirectory = $currentDir
$StartMenuShortcut.Description = "Start Nexus AI Chat Application"
$StartMenuShortcut.IconLocation = "shell32.dll,25"  # Chat bubble icon
$StartMenuShortcut.Save()

Write-Host "Shortcuts created successfully!" -ForegroundColor Green
Write-Host "Desktop shortcut: $shortcutPath" -ForegroundColor Yellow
Write-Host "Start Menu shortcut: $startMenuPath" -ForegroundColor Yellow
Write-Host ""
Write-Host "You can now start the Nexus AI Chat application by:" -ForegroundColor Cyan
Write-Host "1. Double-clicking the desktop shortcut" -ForegroundColor White
Write-Host "2. Searching for 'Nexus AI Chat' in the Start Menu" -ForegroundColor White
Write-Host "3. Running start-nexus.bat directly" -ForegroundColor White

# Pause to show the results
Read-Host "Press Enter to continue..."
