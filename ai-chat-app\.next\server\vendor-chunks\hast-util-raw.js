"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-raw";
exports.ids = ["vendor-chunks/hast-util-raw"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-raw/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/hast-util-raw/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   raw: () => (/* binding */ raw)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-from-parse5 */ \"(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\");\n/* harmony import */ var hast_util_to_parse5__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! hast-util-to-parse5 */ \"(ssr)/./node_modules/hast-util-to-parse5/lib/index.js\");\n/* harmony import */ var html_void_elements__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! html-void-elements */ \"(ssr)/./node_modules/html-void-elements/index.js\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parse5 */ \"(ssr)/./node_modules/parse5/dist/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/web-namespaces/index.js\");\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/zwitch/index.js\");\n/**\n * @import {Options} from 'hast-util-raw'\n * @import {Comment, Doctype, Element, Nodes, RootContent, Root, Text} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {DefaultTreeAdapterMap, ParserOptions} from 'parse5'\n * @import {Point} from 'unist'\n */\n\n/**\n * @typedef State\n *   Info passed around about the current state.\n * @property {(node: Nodes) => undefined} handle\n *   Add a hast node to the parser.\n * @property {Options} options\n *   User configuration.\n * @property {Parser<DefaultTreeAdapterMap>} parser\n *   Current parser.\n * @property {boolean} stitches\n *   Whether there are stitches.\n */\n\n/**\n * @typedef Stitch\n *   Custom comment-like value we pass through parse5, which contains a\n *   replacement node that we’ll swap back in afterwards.\n * @property {'comment'} type\n *   Node type.\n * @property {{stitch: Nodes}} value\n *   Replacement value.\n */\n\n\n\n\n\n\n\n\n\n\n\nconst gfmTagfilterExpression =\n  /<(\\/?)(iframe|noembed|noframes|plaintext|script|style|textarea|title|xmp)(?=[\\t\\n\\f\\r />])/gi\n\n// Node types associated with MDX.\n// <https://github.com/mdx-js/mdx/blob/8a56312/packages/mdx/lib/node-types.js>\nconst knownMdxNames = new Set([\n  'mdxFlowExpression',\n  'mdxJsxFlowElement',\n  'mdxJsxTextElement',\n  'mdxTextExpression',\n  'mdxjsEsm'\n])\n\n/** @type {ParserOptions<DefaultTreeAdapterMap>} */\nconst parseOptions = {sourceCodeLocationInfo: true, scriptingEnabled: false}\n\n/**\n * Pass a hast tree through an HTML parser, which will fix nesting, and turn\n * raw nodes into actual nodes.\n *\n * @param {Nodes} tree\n *   Original hast tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Nodes}\n *   Parsed again tree.\n */\nfunction raw(tree, options) {\n  const document = documentMode(tree)\n  /** @type {(node: Nodes, state: State) => undefined} */\n  const one = (0,zwitch__WEBPACK_IMPORTED_MODULE_1__.zwitch)('type', {\n    handlers: {root, element, text, comment, doctype, raw: handleRaw},\n    unknown\n  })\n\n  /** @type {State} */\n  const state = {\n    parser: document\n      ? new parse5__WEBPACK_IMPORTED_MODULE_0__.Parser(parseOptions)\n      : parse5__WEBPACK_IMPORTED_MODULE_0__.Parser.getFragmentParser(undefined, parseOptions),\n    handle(node) {\n      one(node, state)\n    },\n    stitches: false,\n    options: options || {}\n  }\n\n  one(tree, state)\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)())\n\n  const p5 = document ? state.parser.document : state.parser.getFragment()\n  const result = (0,hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_3__.fromParse5)(p5, {\n    // To do: support `space`?\n    file: state.options.file\n  })\n\n  if (state.stitches) {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(result, 'comment', function (node, index, parent) {\n      const stitch = /** @type {Stitch} */ (/** @type {unknown} */ (node))\n      if (stitch.value.stitch && parent && index !== undefined) {\n        /** @type {Array<RootContent>} */\n        const siblings = parent.children\n        // @ts-expect-error: assume the stitch is allowed.\n        siblings[index] = stitch.value.stitch\n        return index\n      }\n    })\n  }\n\n  // Unpack if possible and when not given a `root`.\n  if (\n    result.type === 'root' &&\n    result.children.length === 1 &&\n    result.children[0].type === tree.type\n  ) {\n    return result.children[0]\n  }\n\n  return result\n}\n\n/**\n * Transform all nodes\n *\n * @param {Array<RootContent>} nodes\n *   hast content.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction all(nodes, state) {\n  let index = -1\n\n  /* istanbul ignore else - invalid nodes, see rehypejs/rehype-raw#7. */\n  if (nodes) {\n    while (++index < nodes.length) {\n      state.handle(nodes[index])\n    }\n  }\n}\n\n/**\n * Transform a root.\n *\n * @param {Root} node\n *   hast root node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction root(node, state) {\n  all(node.children, state)\n}\n\n/**\n * Transform an element.\n *\n * @param {Element} node\n *   hast element node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction element(node, state) {\n  startTag(node, state)\n\n  all(node.children, state)\n\n  endTag(node, state)\n}\n\n/**\n * Transform a text.\n *\n * @param {Text} node\n *   hast text node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction text(node, state) {\n  // Allow `DATA` through `PLAINTEXT`,\n  // but when hanging in a tag for example,\n  // switch back to `DATA`.\n  // Note: `State` is not exposed by `parse5`, so these numbers are fragile.\n  // See: <https://github.com/inikulin/parse5/blob/46cba43/packages/parse5/lib/tokenizer/index.ts#L58>\n  if (state.parser.tokenizer.state > 4) {\n    state.parser.tokenizer.state = 0\n  }\n\n  /** @type {Token.CharacterToken} */\n  const token = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.CHARACTER,\n    chars: node.value,\n    location: createParse5Location(node)\n  }\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a doctype.\n *\n * @param {Doctype} node\n *   hast doctype node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction doctype(node, state) {\n  /** @type {Token.DoctypeToken} */\n  const token = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.DOCTYPE,\n    name: 'html',\n    forceQuirks: false,\n    publicId: '',\n    systemId: '',\n    location: createParse5Location(node)\n  }\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a stitch.\n *\n * @param {Nodes} node\n *   unknown node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction stitch(node, state) {\n  // Mark that there are stitches, so we need to walk the tree and revert them.\n  state.stitches = true\n\n  /** @type {Nodes} */\n  const clone = cloneWithoutChildren(node)\n\n  // Recurse, because to somewhat handle `[<x>]</x>` (where `[]` denotes the\n  // passed through node).\n  if ('children' in node && 'children' in clone) {\n    // Root in root out.\n    const fakeRoot = /** @type {Root} */ (\n      raw({type: 'root', children: node.children}, state.options)\n    )\n    clone.children = fakeRoot.children\n  }\n\n  // Hack: `value` is supposed to be a string, but as none of the tools\n  // (`parse5` or `hast-util-from-parse5`) looks at it, we can pass nodes\n  // through.\n  comment({type: 'comment', value: {stitch: clone}}, state)\n}\n\n/**\n * Transform a comment (or stitch).\n *\n * @param {Comment | Stitch} node\n *   hast comment node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction comment(node, state) {\n  /** @type {string} */\n  // @ts-expect-error: we pass stitches through.\n  const data = node.value\n\n  /** @type {Token.CommentToken} */\n  const token = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.COMMENT,\n    data,\n    location: createParse5Location(node)\n  }\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a raw node.\n *\n * @param {Raw} node\n *   hast raw node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction handleRaw(node, state) {\n  // Reset preprocessor:\n  // See: <https://github.com/inikulin/parse5/blob/6f7ca60/packages/parse5/lib/tokenizer/preprocessor.ts#L18-L31>.\n  state.parser.tokenizer.preprocessor.html = ''\n  state.parser.tokenizer.preprocessor.pos = -1\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.lastGapPos = -2\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.gapStack = []\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.skipNextNewLine = false\n  state.parser.tokenizer.preprocessor.lastChunkWritten = false\n  state.parser.tokenizer.preprocessor.endOfChunkHit = false\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.isEol = false\n\n  // Now pass `node.value`.\n  setPoint(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n\n  state.parser.tokenizer.write(\n    state.options.tagfilter\n      ? node.value.replace(gfmTagfilterExpression, '&lt;$1$2')\n      : node.value,\n    false\n  )\n  // @ts-expect-error: private.\n  state.parser.tokenizer._runParsingLoop()\n\n  // Character references hang, so if we ended there, we need to flush\n  // those too.\n  // We reset the preprocessor as if the document ends here.\n  // Then one single call to the relevant state does the trick, parse5\n  // consumes the whole token.\n\n  // Note: `State` is not exposed by `parse5`, so these numbers are fragile.\n  // See: <https://github.com/inikulin/parse5/blob/46cba43/packages/parse5/lib/tokenizer/index.ts#L58>\n  // Note: a change to `parse5`, which breaks this, was merged but not released.\n  // Investigate when it is.\n  // To do: remove next major.\n  /* c8 ignore next 12 -- removed in <https://github.com/inikulin/parse5/pull/897> */\n  if (\n    state.parser.tokenizer.state === 72 /* NAMED_CHARACTER_REFERENCE */ ||\n    // @ts-expect-error: removed.\n    state.parser.tokenizer.state === 78 /* NUMERIC_CHARACTER_REFERENCE_END */\n  ) {\n    state.parser.tokenizer.preprocessor.lastChunkWritten = true\n    /** @type {number} */\n    // @ts-expect-error: private.\n    const cp = state.parser.tokenizer._consume()\n    // @ts-expect-error: private.\n    state.parser.tokenizer._callState(cp)\n  }\n}\n\n/**\n * Crash on an unknown node.\n *\n * @param {unknown} node_\n *   unknown node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Never.\n */\nfunction unknown(node_, state) {\n  const node = /** @type {Nodes} */ (node_)\n\n  if (\n    state.options.passThrough &&\n    state.options.passThrough.includes(node.type)\n  ) {\n    stitch(node, state)\n  } else {\n    let extra = ''\n\n    if (knownMdxNames.has(node.type)) {\n      extra =\n        \". It looks like you are using MDX nodes with `hast-util-raw` (or `rehype-raw`). If you use this because you are using remark or rehype plugins that inject `'html'` nodes, then please raise an issue with that plugin, as its a bad and slow idea. If you use this because you are using markdown syntax, then you have to configure this utility (or plugin) to pass through these nodes (see `passThrough` in docs), but you can also migrate to use the MDX syntax\"\n    }\n\n    throw new Error('Cannot compile `' + node.type + '` node' + extra)\n  }\n}\n\n/**\n * Reset the tokenizer of a parser.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Point | undefined} point\n *   Point.\n * @returns {undefined}\n *   Nothing.\n */\nfunction resetTokenizer(state, point) {\n  setPoint(state, point)\n\n  // Process final characters if they’re still there after hibernating.\n  /** @type {Token.CharacterToken} */\n  // @ts-expect-error: private.\n  const token = state.parser.tokenizer.currentCharacterToken\n\n  if (token && token.location) {\n    token.location.endLine = state.parser.tokenizer.preprocessor.line\n    token.location.endCol = state.parser.tokenizer.preprocessor.col + 1\n    token.location.endOffset = state.parser.tokenizer.preprocessor.offset + 1\n    // @ts-expect-error: private.\n    state.parser.currentToken = token\n    // @ts-expect-error: private.\n    state.parser._processToken(state.parser.currentToken)\n  }\n\n  // Reset tokenizer:\n  // See: <https://github.com/inikulin/parse5/blob/6f7ca60/packages/parse5/lib/tokenizer/index.ts#L187-L223>.\n  // Especially putting it back in the `data` state is useful: some elements,\n  // like textareas and iframes, change the state.\n  // See GH-7.\n  // But also if broken HTML is in `raw`, and then a correct element is given.\n  // See GH-11.\n  // @ts-expect-error: private.\n  state.parser.tokenizer.paused = false\n  // @ts-expect-error: private.\n  state.parser.tokenizer.inLoop = false\n\n  // Note: don’t reset `state`, `inForeignNode`, or `lastStartTagName`, we\n  // manually update those when needed.\n  state.parser.tokenizer.active = false\n  // @ts-expect-error: private.\n  state.parser.tokenizer.returnState = parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.DATA\n  // @ts-expect-error: private.\n  state.parser.tokenizer.charRefCode = -1\n  // @ts-expect-error: private.\n  state.parser.tokenizer.consumedAfterSnapshot = -1\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentLocation = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentCharacterToken = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentToken = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentAttr = {name: '', value: ''}\n}\n\n/**\n * Set current location.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Point | undefined} point\n *   Point.\n * @returns {undefined}\n *   Nothing.\n */\nfunction setPoint(state, point) {\n  if (point && point.offset !== undefined) {\n    /** @type {Token.Location} */\n    const location = {\n      startLine: point.line,\n      startCol: point.column,\n      startOffset: point.offset,\n      endLine: -1,\n      endCol: -1,\n      endOffset: -1\n    }\n\n    // @ts-expect-error: private.\n    // type-coverage:ignore-next-line\n    state.parser.tokenizer.preprocessor.lineStartPos = -point.column + 1 // Looks weird, but ensures we get correct positional info.\n    state.parser.tokenizer.preprocessor.droppedBufferSize = point.offset\n    state.parser.tokenizer.preprocessor.line = point.line\n    // @ts-expect-error: private.\n    state.parser.tokenizer.currentLocation = location\n  }\n}\n\n/**\n * Emit a start tag.\n *\n * @param {Element} node\n *   Element.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction startTag(node, state) {\n  const tagName = node.tagName.toLowerCase()\n\n  // Ignore tags if we’re in plain text.\n  if (state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.PLAINTEXT) return\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n\n  const current = state.parser.openElements.current\n  let ns = 'namespaceURI' in current ? current.namespaceURI : web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.html\n\n  if (ns === web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.html && tagName === 'svg') {\n    ns = web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.svg\n  }\n\n  const result = (0,hast_util_to_parse5__WEBPACK_IMPORTED_MODULE_6__.toParse5)(\n    // Shallow clone to not delve into `children`: we only need the attributes.\n    {...node, children: []},\n    {space: ns === web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.svg ? 'svg' : 'html'}\n  )\n\n  /** @type {Token.TagToken} */\n  const tag = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.START_TAG,\n    tagName,\n    tagID: parse5__WEBPACK_IMPORTED_MODULE_0__.html.getTagID(tagName),\n    // We always send start and end tags.\n    selfClosing: false,\n    ackSelfClosing: false,\n    // Always element.\n    /* c8 ignore next */\n    attrs: 'attrs' in result ? result.attrs : [],\n    location: createParse5Location(node)\n  }\n\n  // The HTML parsing algorithm works by doing half of the state management in\n  // the tokenizer and half in the parser.\n  // We can’t use the tokenizer here, as we don’t have strings.\n  // So we act *as if* the tokenizer emits tokens:\n\n  // @ts-expect-error: private.\n  state.parser.currentToken = tag\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n\n  // …but then we still need a bunch of work that the tokenizer would normally\n  // do, such as:\n\n  // Set a tag name, similar to how the tokenizer would do it.\n  state.parser.tokenizer.lastStartTagName = tagName\n\n  // `inForeignNode` is correctly set by the parser.\n}\n\n/**\n * Emit an end tag.\n *\n * @param {Element} node\n *   Element.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction endTag(node, state) {\n  const tagName = node.tagName.toLowerCase()\n  // Do not emit closing tags for HTML void elements.\n  if (\n    !state.parser.tokenizer.inForeignNode &&\n    html_void_elements__WEBPACK_IMPORTED_MODULE_7__.htmlVoidElements.includes(tagName)\n  ) {\n    return\n  }\n\n  // Ignore tags if we’re in plain text.\n  if (state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.PLAINTEXT) return\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointEnd)(node))\n\n  /** @type {Token.TagToken} */\n  const tag = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.END_TAG,\n    tagName,\n    tagID: parse5__WEBPACK_IMPORTED_MODULE_0__.html.getTagID(tagName),\n    selfClosing: false,\n    ackSelfClosing: false,\n    attrs: [],\n    location: createParse5Location(node)\n  }\n\n  // The HTML parsing algorithm works by doing half of the state management in\n  // the tokenizer and half in the parser.\n  // We can’t use the tokenizer here, as we don’t have strings.\n  // So we act *as if* the tokenizer emits tokens:\n\n  // @ts-expect-error: private.\n  state.parser.currentToken = tag\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n\n  // …but then we still need a bunch of work that the tokenizer would normally\n  // do, such as:\n\n  // Switch back to the data state after alternative states that don’t accept\n  // tags:\n  if (\n    // Current element is closed.\n    tagName === state.parser.tokenizer.lastStartTagName &&\n    // `<textarea>` and `<title>`\n    (state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.RCDATA ||\n      // `<iframe>`, `<noembed>`, `<noframes>`, `<style>`, `<xmp>`\n      state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.RAWTEXT ||\n      // `<script>`\n      state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.SCRIPT_DATA)\n    // Note: `<plaintext>` not needed, as it’s the last element.\n  ) {\n    state.parser.tokenizer.state = parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.DATA\n  }\n}\n\n/**\n * Check if `node` represents a whole document or a fragment.\n *\n * @param {Nodes} node\n *   hast node.\n * @returns {boolean}\n *   Whether this represents a whole document or a fragment.\n */\nfunction documentMode(node) {\n  const head = node.type === 'root' ? node.children[0] : node\n  return Boolean(\n    head &&\n      (head.type === 'doctype' ||\n        (head.type === 'element' && head.tagName.toLowerCase() === 'html'))\n  )\n}\n\n/**\n * Get a `parse5` location from a node.\n *\n * @param {Nodes | Stitch} node\n *   hast node.\n * @returns {Token.Location}\n *   `parse5` location.\n */\nfunction createParse5Location(node) {\n  const start = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node) || {\n    line: undefined,\n    column: undefined,\n    offset: undefined\n  }\n  const end = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointEnd)(node) || {\n    line: undefined,\n    column: undefined,\n    offset: undefined\n  }\n\n  /** @type {Record<keyof Token.Location, number | undefined>} */\n  const location = {\n    startLine: start.line,\n    startCol: start.column,\n    startOffset: start.offset,\n    endLine: end.line,\n    endCol: end.column,\n    endOffset: end.offset\n  }\n\n  // @ts-expect-error: unist point values can be `undefined` in hast, which\n  // `parse5` types don’t want.\n  return location\n}\n\n/**\n * @template {Nodes} NodeType\n *   Node type.\n * @param {NodeType} node\n *   Node to clone.\n * @returns {NodeType}\n *   Cloned node, without children.\n */\nfunction cloneWithoutChildren(node) {\n  return 'children' in node\n    ? (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({...node, children: []})\n    : (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-raw/lib/index.js\n");

/***/ })

};
;