/**
 * Utility functions for parsing and formatting <tool> tags in AI responses
 */

export interface ToolCall {
  name: string;
  arguments: Record<string, any>;
  summary?: string;
  final_message?: string;
}

/**
 * Parses a <tool> tag content and extracts the JSON data
 */
export function parseToolTag(toolContent: string): ToolCall | null {
  try {
    // Remove any extra whitespace and newlines
    const cleanContent = toolContent.trim();
    
    // Try to parse as JSON
    const parsed = JSON.parse(cleanContent);
    
    // Validate that it has the expected structure
    if (typeof parsed === 'object' && parsed !== null) {
      return {
        name: parsed.name || 'unknown',
        arguments: parsed.arguments || {},
        summary: parsed.summary,
        final_message: parsed.final_message
      };
    }
    
    return null;
  } catch (error) {
    console.warn('Failed to parse tool tag content:', error);
    return null;
  }
}

/**
 * Formats a tool call into a readable markdown representation
 */
export function formatToolCall(toolCall: ToolCall): string {
  const { name, arguments: args, summary, final_message } = toolCall;
  
  let formatted = `### 🔧 Tool Call: ${name}\n\n`;
  
  // Add summary if available
  if (summary) {
    formatted += `**Summary:** ${summary}\n\n`;
  }
  
  // Add arguments if they exist and are not empty
  if (args && Object.keys(args).length > 0) {
    formatted += `**Arguments:**\n`;
    formatted += '```json\n';
    formatted += JSON.stringify(args, null, 2);
    formatted += '\n```\n\n';
  }
  
  // Add final message if available
  if (final_message) {
    formatted += `**Result:** ${final_message}\n\n`;
  }
  
  return formatted;
}

/**
 * Processes message content to replace <tool> tags with formatted content
 */
export function processToolTags(content: string): string {
  // Regular expression to match <tool>...</tool> tags (including variations like <tool call>)
  const toolTagRegex = /<tool[^>]*>([\s\S]*?)<\/tool[^>]*>/gi;
  
  return content.replace(toolTagRegex, (match, toolContent) => {
    const toolCall = parseToolTag(toolContent);
    
    if (toolCall) {
      return formatToolCall(toolCall);
    } else {
      // If parsing fails, wrap in a code block to make it more readable
      return `\n\`\`\`\n${toolContent.trim()}\n\`\`\`\n`;
    }
  });
}

/**
 * Alternative processing that creates collapsible sections for tool calls
 */
export function processToolTagsWithCollapse(content: string): string {
  const toolTagRegex = /<tool[^>]*>([\s\S]*?)<\/tool[^>]*>/gi;
  
  return content.replace(toolTagRegex, (match, toolContent) => {
    const toolCall = parseToolTag(toolContent);
    
    if (toolCall) {
      const { name, arguments: args, summary, final_message } = toolCall;
      
      let formatted = `<details>\n<summary><strong>🔧 Tool Call: ${name}</strong>`;
      if (summary) {
        formatted += ` - ${summary}`;
      }
      formatted += `</summary>\n\n`;
      
      if (args && Object.keys(args).length > 0) {
        formatted += `**Arguments:**\n\`\`\`json\n${JSON.stringify(args, null, 2)}\n\`\`\`\n\n`;
      }
      
      if (final_message) {
        formatted += `**Result:** ${final_message}\n\n`;
      }
      
      formatted += `</details>\n\n`;
      
      return formatted;
    } else {
      // If parsing fails, wrap in a collapsible code block
      return `<details>\n<summary><strong>🔧 Tool Output</strong></summary>\n\n\`\`\`\n${toolContent.trim()}\n\`\`\`\n\n</details>\n\n`;
    }
  });
}
