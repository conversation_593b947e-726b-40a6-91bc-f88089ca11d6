@echo off
echo Starting Nexus AI Chat Application...
echo.

REM Check if Dock<PERSON> is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

echo Docker is running. Starting Nexus services...
echo.

REM Stop any existing containers
echo Stopping existing containers...
docker-compose down

REM Build and start the services
echo Building and starting services...
docker-compose up --build -d

REM Wait for services to be ready
echo.
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check service health
echo.
echo Checking service health...
docker-compose ps

echo.
echo ========================================
echo Nexus AI Chat Application is starting!
echo ========================================
echo.
echo Web Application: http://localhost:3000
echo Database: localhost:5433
echo AI Service: http://localhost:8000
echo.
echo To stop the application, run: docker-compose down
echo To view logs, run: docker-compose logs -f
echo.

REM Open the application in browser
echo Opening application in browser...
start http://localhost:3000

echo.
echo Press any key to view logs (Ctrl+C to exit logs)...
pause >nul
docker-compose logs -f
