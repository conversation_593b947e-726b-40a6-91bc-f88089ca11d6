import { Pool, PoolClient } from 'pg';
import { Conversation, Message, Workspace } from '@/types/chat';

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'ai_chat_app',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'nexus123',
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000, // Increased timeout
  // Disable SSL for local development
  ssl: false,
};

// Debug logging
console.log('Database config:', {
  host: dbConfig.host,
  port: dbConfig.port,
  database: dbConfig.database,
  user: dbConfig.user,
  password: dbConfig.password === '' ? 'empty_string' : (dbConfig.password ? '***' : 'undefined')
});

// Create connection pool
let pool: Pool | null = null;

function getPool(): Pool {
  if (!pool) {
    pool = new Pool(dbConfig);
    
    // Handle pool errors
    pool.on('error', (err) => {
      console.error('Unexpected error on idle client', err);
    });
  }
  return pool;
}

// Database utility functions
export class DatabaseService {
  private static async getClient(): Promise<PoolClient> {
    const pool = getPool();
    return await pool.connect();
  }

  // Initialize database (create tables if they don't exist)
  static async initialize(): Promise<void> {
    const client = await this.getClient();
    try {
      // Read and execute schema
      const fs = require('fs').promises;
      const path = require('path');
      const schemaPath = path.join(process.cwd(), 'database', 'schema.sql');
      
      try {
        const schema = await fs.readFile(schemaPath, 'utf-8');
        await client.query(schema);
        console.log('Database schema initialized successfully');
      } catch (error) {
        console.warn('Could not read schema file, database may need manual setup:', error);
      }
    } finally {
      client.release();
    }
  }

  // User management
  static async findOrCreateUser(deviceId: string): Promise<string> {
    const client = await this.getClient();
    try {
      // Try to find existing user
      const findResult = await client.query(
        'SELECT id FROM users WHERE device_id = $1',
        [deviceId]
      );

      if (findResult.rows.length > 0) {
        return findResult.rows[0].id;
      }

      // Create new user
      const createResult = await client.query(
        'INSERT INTO users (device_id) VALUES ($1) RETURNING id',
        [deviceId]
      );

      return createResult.rows[0].id;
    } finally {
      client.release();
    }
  }

  // Conversation management
  static async saveConversations(deviceId: string, conversations: Conversation[]): Promise<void> {
    const client = await this.getClient();
    try {
      await client.query('BEGIN');

      const userId = await this.findOrCreateUser(deviceId);

      for (const conversation of conversations) {
        // Upsert conversation
        await client.query(`
          INSERT INTO conversations (id, user_id, workspace_id, title, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6)
          ON CONFLICT (id) DO UPDATE SET
            workspace_id = EXCLUDED.workspace_id,
            title = EXCLUDED.title,
            updated_at = EXCLUDED.updated_at
        `, [
          conversation.id,
          userId,
          conversation.workspaceId || null,
          conversation.title,
          new Date(conversation.createdAt),
          new Date(conversation.updatedAt)
        ]);

        // Delete existing messages for this conversation
        await client.query(
          'DELETE FROM messages WHERE conversation_id = $1',
          [conversation.id]
        );

        // Insert messages
        for (const message of conversation.messages) {
          await client.query(`
            INSERT INTO messages (id, conversation_id, role, content, timestamp, is_streaming)
            VALUES ($1, $2, $3, $4, $5, $6)
          `, [
            message.id,
            conversation.id,
            message.role,
            message.content,
            new Date(message.timestamp),
            message.isStreaming || false
          ]);
        }
      }

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  static async deleteMessage(deviceId: string, conversationId: string, messageId: string): Promise<void> {
    const client = await this.getClient();
    try {
      await client.query('BEGIN');

      const userId = await this.findOrCreateUser(deviceId);

      // Verify the conversation belongs to this user
      const convResult = await client.query(
        'SELECT id FROM conversations WHERE id = $1 AND user_id = $2',
        [conversationId, userId]
      );

      if (convResult.rows.length === 0) {
        throw new Error('Conversation not found or access denied');
      }

      // Delete the specific message
      const deleteResult = await client.query(
        'DELETE FROM messages WHERE id = $1 AND conversation_id = $2',
        [messageId, conversationId]
      );

      if (deleteResult.rowCount === 0) {
        throw new Error('Message not found');
      }

      // Update conversation's updated_at timestamp
      await client.query(
        'UPDATE conversations SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
        [conversationId]
      );

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  static async loadConversations(deviceId: string): Promise<Conversation[]> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);

      // Load conversations
      const conversationsResult = await client.query(`
        SELECT id, title, workspace_id, created_at, updated_at
        FROM conversations
        WHERE user_id = $1
        ORDER BY updated_at DESC
      `, [userId]);

      const conversations: Conversation[] = [];

      for (const convRow of conversationsResult.rows) {
        // Load messages for this conversation
        const messagesResult = await client.query(`
          SELECT id, role, content, timestamp, is_streaming
          FROM messages
          WHERE conversation_id = $1
          ORDER BY timestamp ASC
        `, [convRow.id]);

        const messages: Message[] = messagesResult.rows.map(msgRow => ({
          id: msgRow.id,
          role: msgRow.role,
          content: msgRow.content,
          timestamp: msgRow.timestamp.getTime(),
          isStreaming: msgRow.is_streaming
        }));

        conversations.push({
          id: convRow.id,
          title: convRow.title,
          messages,
          workspaceId: convRow.workspace_id,
          createdAt: convRow.created_at.getTime(),
          updatedAt: convRow.updated_at.getTime()
        });
      }

      return conversations;
    } finally {
      client.release();
    }
  }

  // Individual conversation operations
  static async deleteConversation(deviceId: string, conversationId: string): Promise<void> {
    const client = await this.getClient();
    try {
      await client.query('BEGIN');

      const userId = await this.findOrCreateUser(deviceId);

      // Verify the conversation belongs to this user
      const convResult = await client.query(
        'SELECT id FROM conversations WHERE id = $1 AND user_id = $2',
        [conversationId, userId]
      );

      if (convResult.rows.length === 0) {
        throw new Error('Conversation not found or access denied');
      }

      // Delete messages first (due to foreign key constraint)
      await client.query(
        'DELETE FROM messages WHERE conversation_id = $1',
        [conversationId]
      );

      // Delete the conversation
      await client.query(
        'DELETE FROM conversations WHERE id = $1',
        [conversationId]
      );

      // Clear current conversation if it's the one being deleted
      await client.query(
        'UPDATE user_current_conversation SET conversation_id = NULL WHERE user_id = $1 AND conversation_id = $2',
        [userId, conversationId]
      );

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  static async updateConversationTitle(deviceId: string, conversationId: string, newTitle: string): Promise<void> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);

      // Verify the conversation belongs to this user and update it
      const result = await client.query(`
        UPDATE conversations 
        SET title = $1, updated_at = CURRENT_TIMESTAMP 
        WHERE id = $2 AND user_id = $3
        RETURNING id
      `, [newTitle, conversationId, userId]);

      if (result.rows.length === 0) {
        throw new Error('Conversation not found or access denied');
      }
    } finally {
      client.release();
    }
  }

  static async updateSingleConversation(deviceId: string, conversation: Conversation): Promise<void> {
    console.log('DatabaseService.updateSingleConversation called:', {
      deviceId,
      conversationId: conversation.id,
      messageCount: conversation.messages.length
    });

    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);
      console.log('Found/created user:', userId);

      // Use UPSERT for conversation to avoid deadlocks
      await client.query(`
        INSERT INTO conversations (id, user_id, workspace_id, title, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (id) DO UPDATE SET
          workspace_id = EXCLUDED.workspace_id,
          title = EXCLUDED.title,
          updated_at = EXCLUDED.updated_at
      `, [
        conversation.id,
        userId,
        conversation.workspaceId || null,
        conversation.title,
        new Date(conversation.createdAt),
        new Date(conversation.updatedAt)
      ]);
      console.log('Upserted conversation');

      // Use a more efficient approach for messages - delete and insert in a single transaction
      // but with shorter transaction scope
      await client.query('BEGIN');

      try {
        // Delete existing messages for this conversation
        const deleteResult = await client.query(
          'DELETE FROM messages WHERE conversation_id = $1',
          [conversation.id]
        );
        console.log('Deleted existing messages:', deleteResult.rowCount);

        // Batch insert messages if there are any
        if (conversation.messages.length > 0) {
          const values = conversation.messages.map((message, index) => {
            const baseIndex = index * 6;
            return `($${baseIndex + 1}, $${baseIndex + 2}, $${baseIndex + 3}, $${baseIndex + 4}, $${baseIndex + 5}, $${baseIndex + 6})`;
          }).join(', ');

          const params = conversation.messages.flatMap(message => [
            message.id,
            conversation.id,
            message.role,
            message.content,
            new Date(message.timestamp),
            message.isStreaming || false
          ]);

          await client.query(`
            INSERT INTO messages (id, conversation_id, role, content, timestamp, is_streaming)
            VALUES ${values}
          `, params);

          console.log('Batch inserted messages:', conversation.messages.length);
        }

        await client.query('COMMIT');
        console.log('Successfully updated conversation in database');
      } catch (error) {
        await client.query('ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error('DatabaseService.updateSingleConversation error:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // Current conversation management
  static async setCurrentConversation(deviceId: string, conversationId: string | null): Promise<void> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);

      await client.query(`
        INSERT INTO user_current_conversation (user_id, conversation_id)
        VALUES ($1, $2)
        ON CONFLICT (user_id) DO UPDATE SET
          conversation_id = EXCLUDED.conversation_id,
          updated_at = CURRENT_TIMESTAMP
      `, [userId, conversationId]);
    } finally {
      client.release();
    }
  }

  static async getCurrentConversation(deviceId: string): Promise<string | null> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);

      const result = await client.query(`
        SELECT conversation_id
        FROM user_current_conversation
        WHERE user_id = $1
      `, [userId]);

      return result.rows.length > 0 ? result.rows[0].conversation_id : null;
    } finally {
      client.release();
    }
  }

  // Settings management
  static async saveUserSettings(deviceId: string, settingsKey: string, settingsValue: any): Promise<void> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);



      // For JSONB columns, PostgreSQL expects a JavaScript object, not a JSON string
      // The pg driver will automatically handle the conversion to JSONB
      if (typeof settingsValue !== 'object' || settingsValue === null) {
        throw new Error(`Settings value must be an object, got ${typeof settingsValue}`);
      }

      await client.query(`
        INSERT INTO user_settings (user_id, settings_key, settings_value)
        VALUES ($1, $2, $3)
        ON CONFLICT (user_id, settings_key) DO UPDATE SET
          settings_value = EXCLUDED.settings_value,
          updated_at = CURRENT_TIMESTAMP
      `, [userId, settingsKey, settingsValue]);


    } finally {
      client.release();
    }
  }

  static async loadUserSettings(deviceId: string, settingsKey: string): Promise<any | null> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);

      const result = await client.query(`
        SELECT settings_value
        FROM user_settings
        WHERE user_id = $1 AND settings_key = $2
      `, [userId, settingsKey]);



      if (result.rows.length > 0) {
        const rawValue = result.rows[0].settings_value;

        // Since the column is JSONB, PostgreSQL returns it as an object, not a string
        // No need to JSON.parse() - it's already parsed by PostgreSQL
        if (rawValue && typeof rawValue === 'object') {
          return rawValue;
        } else {
          console.error(`Invalid settings data type for ${deviceId}:${settingsKey}:`, typeof rawValue);

          // Clear the corrupted data
          await client.query(`
            DELETE FROM user_settings
            WHERE user_id = $1 AND settings_key = $2
          `, [userId, settingsKey]);

          console.log(`Cleared corrupted settings data for ${deviceId}:${settingsKey}`);
          return null;
        }
      }


      return null;
    } finally {
      client.release();
    }
  }

  // Clear all data for a user
  static async clearUserData(deviceId: string): Promise<void> {
    const client = await this.getClient();
    try {
      await client.query('BEGIN');

      const userResult = await client.query(
        'SELECT id FROM users WHERE device_id = $1',
        [deviceId]
      );

      if (userResult.rows.length > 0) {
        const userId = userResult.rows[0].id;
        
        // Delete user data (cascading deletes will handle related records)
        await client.query('DELETE FROM users WHERE id = $1', [userId]);
      }

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Workspace management
  static async loadWorkspaces(deviceId: string): Promise<Workspace[]> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);

      const result = await client.query(`
        SELECT id, name, description, color, position, created_at, updated_at
        FROM workspaces
        WHERE user_id = $1
        ORDER BY position ASC, created_at ASC
      `, [userId]);

      return result.rows.map(row => ({
        id: row.id,
        name: row.name,
        description: row.description,
        color: row.color,
        position: row.position || 0,
        createdAt: row.created_at.getTime(),
        updatedAt: row.updated_at.getTime(),
        userId
      }));
    } finally {
      client.release();
    }
  }

  static async createWorkspace(deviceId: string, workspace: Omit<Workspace, 'id' | 'createdAt' | 'updatedAt' | 'userId'>): Promise<Workspace> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);

      // Get the next position (max position + 1)
      const positionResult = await client.query(`
        SELECT COALESCE(MAX(position), 0) + 1 as next_position
        FROM workspaces
        WHERE user_id = $1
      `, [userId]);

      const nextPosition = workspace.position ?? positionResult.rows[0].next_position;

      const result = await client.query(`
        INSERT INTO workspaces (user_id, name, description, color, position)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id, name, description, color, position, created_at, updated_at
      `, [userId, workspace.name, workspace.description, workspace.color, nextPosition]);

      const row = result.rows[0];
      return {
        id: row.id,
        name: row.name,
        description: row.description,
        color: row.color,
        position: row.position,
        createdAt: row.created_at.getTime(),
        updatedAt: row.updated_at.getTime(),
        userId
      };
    } finally {
      client.release();
    }
  }

  static async updateWorkspace(deviceId: string, workspace: Workspace): Promise<void> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);

      await client.query(`
        UPDATE workspaces
        SET name = $3, description = $4, color = $5, position = $6, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND user_id = $2
      `, [workspace.id, userId, workspace.name, workspace.description, workspace.color, workspace.position]);
    } finally {
      client.release();
    }
  }

  static async deleteWorkspace(deviceId: string, workspaceId: string): Promise<void> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);

      // Delete workspace (conversations will have workspace_id set to NULL due to ON DELETE SET NULL)
      await client.query(`
        DELETE FROM workspaces
        WHERE id = $1 AND user_id = $2
      `, [workspaceId, userId]);
    } finally {
      client.release();
    }
  }

  static async reorderWorkspaces(deviceId: string, workspaceIds: string[]): Promise<void> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);

      // Update positions based on the new order
      for (let i = 0; i < workspaceIds.length; i++) {
        await client.query(`
          UPDATE workspaces
          SET position = $3, updated_at = CURRENT_TIMESTAMP
          WHERE id = $1 AND user_id = $2
        `, [workspaceIds[i], userId, i + 1]);
      }
    } finally {
      client.release();
    }
  }

  static async assignConversationToWorkspace(deviceId: string, conversationId: string, workspaceId: string | null): Promise<void> {
    const client = await this.getClient();
    try {
      const userId = await this.findOrCreateUser(deviceId);
      console.log('DB: Assigning conversation', conversationId, 'to workspace', workspaceId, 'for user', userId);

      const result = await client.query(`
        UPDATE conversations
        SET workspace_id = $3, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1 AND user_id = $2
      `, [conversationId, userId, workspaceId]);

      console.log('DB: Update result:', result.rowCount, 'rows affected');
    } finally {
      client.release();
    }
  }

  // Health check
  static async healthCheck(): Promise<boolean> {
    try {
      const client = await this.getClient();
      try {
        await client.query('SELECT 1');
        return true;
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }

  // Close pool (for graceful shutdown)
  static async close(): Promise<void> {
    if (pool) {
      await pool.end();
      pool = null;
    }
  }
}

// Initialize database on module load
if (process.env.NODE_ENV !== 'test') {
  DatabaseService.initialize().catch(console.error);
}