import { NextRequest, NextResponse } from 'next/server';
import { Workspace } from '@/types/chat';
import { DatabaseService } from '@/lib/database';

function getDeviceIdFromRequest(request: NextRequest): string {
  const { searchParams } = new URL(request.url);
  const deviceId = searchParams.get('deviceId');
  if (!deviceId) {
    throw new Error('Device ID is required');
  }
  return deviceId;
}

// PUT /api/storage/workspaces/[id] - Update a workspace
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const workspaceId = resolvedParams.id;
    const deviceId = getDeviceIdFromRequest(request);
    const body = await request.json();

    if (!deviceId) {
      return NextResponse.json(
        { error: 'Device ID is required' },
        { status: 400 }
      );
    }

    if (!workspaceId) {
      return NextResponse.json(
        { error: 'Workspace ID is required' },
        { status: 400 }
      );
    }

    // Handle workspace updates
    if (body.workspace && typeof body.workspace === 'object') {
      const workspace = body.workspace as Workspace;
      
      if (workspace.id !== workspaceId) {
        return NextResponse.json(
          { error: 'Workspace ID mismatch' },
          { status: 400 }
        );
      }

      await DatabaseService.updateWorkspace(deviceId, workspace);
      return NextResponse.json({ success: true });
    }

    return NextResponse.json(
      { error: 'Invalid request body. Expected workspace object.' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Failed to update workspace:', error);
    return NextResponse.json(
      { error: 'Failed to update workspace' },
      { status: 500 }
    );
  }
}

// DELETE /api/storage/workspaces/[id] - Delete a workspace
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const workspaceId = resolvedParams.id;
    const deviceId = getDeviceIdFromRequest(request);

    if (!deviceId) {
      return NextResponse.json(
        { error: 'Device ID is required' },
        { status: 400 }
      );
    }

    if (!workspaceId) {
      return NextResponse.json(
        { error: 'Workspace ID is required' },
        { status: 400 }
      );
    }

    await DatabaseService.deleteWorkspace(deviceId, workspaceId);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete workspace:', error);
    return NextResponse.json(
      { error: 'Failed to delete workspace' },
      { status: 500 }
    );
  }
}
