'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useEnhancedChat } from '@/hooks/useEnhancedChat';
import { useSettings } from '@/contexts/SettingsContext';
import { useFileWatcher } from '@/hooks/useFileWatcher';
import { StorageService } from '@/lib/storage';
import { Workspace } from '@/types/chat';
import Sidebar from '@/components/Sidebar';
import ChatArea from '@/components/ChatArea';
import ChatInput from '@/components/ChatInput';
import { SettingsModal } from '@/components/settings/SettingsModal';
import { SyncStatus } from '@/components/SyncStatus';

import { Settings, Brain, FileText } from 'lucide-react';
import { APIService } from '@/lib/api';

export default function Home() {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [currentWorkspaceId, setCurrentWorkspaceId] = useState<string | null>(null);
  const { settings } = useSettings();
  const router = useRouter();
  
  // Initialize file watcher for notifications
  const { isWatching } = useFileWatcher(true);

  // Reload workspaces function
  const reloadWorkspaces = async () => {
    try {
      const loadedWorkspaces = await StorageService.loadWorkspaces();
      setWorkspaces(loadedWorkspaces);
    } catch (error) {
      console.error('Failed to load workspaces:', error);
    }
  };

  // Load workspaces on component mount
  useEffect(() => {
    reloadWorkspaces();
  }, []);
  
  // Keyboard shortcut for settings (Ctrl/Cmd + ,)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === ',') {
        e.preventDefault();
        setIsSettingsOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);
  
  const {
    conversations,
    currentConversation,
    currentConversationId,
    isLoading,
    error,
    sidebarOpen,
    currentMode,
    progressStatus,
    serviceHealth,
    createNewConversation,
    selectConversation,
    deleteConversation,
    renameConversation,
    sendMessage,
    editMessage,
    resendMessage,
    deleteMessage,
    setMode,
    toggleSidebar,
    clearError,
    checkServiceHealth,
    reloadConversations,
    enableZepForConversation
  } = useEnhancedChat();

  // Debug: Log current conversation changes
  useEffect(() => {
    console.log('Page: currentConversation changed:', {
      conversationId: currentConversation?.id,
      messageCount: currentConversation?.messages?.length || 0
    });
  }, [currentConversation?.id, currentConversation?.messages?.length]);

  const handleNewChat = async (workspaceId?: string) => {
    console.log('handleNewChat called with workspaceId:', { workspaceId, type: typeof workspaceId });
    const newConversationId = await createNewConversation();

    // If a workspaceId is provided, assign the conversation to that workspace
    if (workspaceId && typeof workspaceId === 'string' && newConversationId) {
      try {
        await handleAssignConversationToWorkspace(newConversationId, workspaceId);
      } catch (error) {
        console.error('Failed to assign new conversation to workspace:', error);
      }
    }
  };

  const handleSendMessage = (message: string) => {
    sendMessage(message);
  };

  const handleRefineText = async (text: string): Promise<string> => {
    if (!text.trim()) return text;
    
    try {
      // Use the preferred refine model from settings
      const refineModel = settings.preferredModels.refine;
      const refinedText = await APIService.refineText(text, refineModel);
      return refinedText;
    } catch (error) {
      console.error('Error refining text:', error);
      return text; // Return original text on error
      throw error;
    }
  };

  const handleEnableZepForCurrentConversation = async () => {
    if (!currentConversationId) {
      alert('No conversation selected');
      return;
    }

    if (!settings.zepSettings.enabled) {
      alert('Zep memory is not enabled. Please enable it in settings first.');
      return;
    }

    try {
      const success = await enableZepForConversation(currentConversationId);
      if (success) {
        alert('Zep memory has been enabled for this conversation! You can now save messages to memory.');
      } else {
        alert('Failed to enable Zep memory. The conversation may already have Zep enabled or there was an error.');
      }
    } catch (error) {
      console.error('Error enabling Zep:', error);
      alert('Failed to enable Zep memory. Please check the console for details.');
    }
  };

  // Workspace management functions
  const handleSelectWorkspace = (workspaceId: string | null) => {
    setCurrentWorkspaceId(workspaceId);
  };

  const handleCreateWorkspace = async (workspace: Omit<Workspace, 'id' | 'createdAt' | 'updatedAt' | 'userId'>) => {
    try {
      const newWorkspace = await StorageService.createWorkspace(workspace);
      setWorkspaces(prev => [...prev, newWorkspace]);
    } catch (error) {
      console.error('Failed to create workspace:', error);
    }
  };

  const handleUpdateWorkspace = async (workspace: Workspace) => {
    try {
      await StorageService.updateWorkspace(workspace);
      setWorkspaces(prev => prev.map(w => w.id === workspace.id ? workspace : w));
    } catch (error) {
      console.error('Failed to update workspace:', error);
    }
  };

  const handleDeleteWorkspace = async (workspaceId: string) => {
    try {
      await StorageService.deleteWorkspace(workspaceId);
      setWorkspaces(prev => prev.filter(w => w.id !== workspaceId));
      if (currentWorkspaceId === workspaceId) {
        setCurrentWorkspaceId(null);
      }
      // Reload conversations to update workspace assignments
      await reloadConversations();
    } catch (error) {
      console.error('Failed to delete workspace:', error);
    }
  };

  const handleReorderWorkspaces = async (workspaceIds: string[]) => {
    try {
      console.log('Page: Reordering workspaces to:', workspaceIds);
      await StorageService.reorderWorkspaces(workspaceIds);
      console.log('Page: Reorder API call completed, reloading workspaces...');
      await reloadWorkspaces();
      console.log('Page: Workspaces reloaded');
    } catch (error) {
      console.error('Failed to reorder workspaces:', error);
    }
  };

  const handleAssignConversationToWorkspace = async (conversationId: string, workspaceId: string | null) => {
    try {
      await StorageService.assignConversationToWorkspace(conversationId, workspaceId);
      // Reload conversations to reflect the change immediately
      await reloadConversations();
    } catch (error) {
      console.error('Failed to assign conversation to workspace:', error);
    }
  };

  // Page component render

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <Sidebar
        conversations={conversations}
        workspaces={workspaces}
        currentConversationId={currentConversationId}
        currentWorkspaceId={currentWorkspaceId}
        isOpen={sidebarOpen}
        onToggle={toggleSidebar}
        onNewChat={handleNewChat}
        onSelectConversation={selectConversation}
        onDeleteConversation={deleteConversation}
        onRenameConversation={renameConversation}
        onSelectWorkspace={handleSelectWorkspace}
        onCreateWorkspace={handleCreateWorkspace}
        onUpdateWorkspace={handleUpdateWorkspace}
        onDeleteWorkspace={handleDeleteWorkspace}
        onReorderWorkspaces={handleReorderWorkspaces}
        onAssignConversationToWorkspace={handleAssignConversationToWorkspace}
        onOpenSettings={() => setIsSettingsOpen(true)}
      />

      <div className="flex-1 flex flex-col h-full overflow-hidden">
        {/* Chat Area - takes remaining space and scrolls internally */}
        <div className="flex-1 min-h-0">
          <ChatArea
            messages={currentConversation?.messages || []}
            isLoading={isLoading}
            error={error}
            currentMode={currentMode}
            progressStatus={progressStatus}
            onEditMessage={editMessage}
            onResendMessage={resendMessage}
            onDeleteMessage={deleteMessage}
            zepSessionId={currentConversation?.zepSessionId}
          />
        </div>

        {/* Chat Input - fixed at bottom */}
        <div className="flex-shrink-0">
          <ChatInput
            onSendMessage={handleSendMessage}
            disabled={isLoading}
            currentMode={currentMode}
            onModeChange={setMode}
            onRefineText={handleRefineText}
            messages={currentConversation?.messages || []}
            zepSessionId={currentConversation?.zepSessionId}
            onEnableZep={handleEnableZepForCurrentConversation}
            placeholder={
              !process.env.NEXT_PUBLIC_CHUTES_API_TOKEN
                ? "Please set NEXT_PUBLIC_CHUTES_API_TOKEN environment variable..."
                : currentMode === 'simple'
                ? "Type your message..."
                : currentMode === 'enhanced'
                ? "Ask me anything - I have access to tools and web search..."
                : "Ask me a complex research question..."
            }
          />
        </div>
      </div>
      
      {/* Settings Modal */}
      <SettingsModal
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
      />


    </div>
  );
}
