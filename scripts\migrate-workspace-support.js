#!/usr/bin/env node

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'ai_chat_app',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
  ssl: process.env.DB_SSL === 'true'
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Starting workspace support migration...');
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', 'add_workspace_support.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Execute the migration
    console.log('📝 Executing migration...');
    const result = await client.query(migrationSQL);
    
    // Check the verification result
    if (result.length > 0) {
      const verification = result[result.length - 1].rows[0];
      console.log('✅ Migration verification:');
      console.log(`   Status: ${verification.status}`);
      console.log(`   Workspaces table exists: ${verification.workspaces_table_exists === '1' ? 'Yes' : 'No'}`);
      console.log(`   Workspace_id column exists: ${verification.workspace_id_column_exists === '1' ? 'Yes' : 'No'}`);
    }
    
    console.log('🎉 Workspace support migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the migration
runMigration().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
