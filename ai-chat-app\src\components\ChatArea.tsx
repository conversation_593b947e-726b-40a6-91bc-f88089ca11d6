'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Message } from '@/types/chat';
import MessageBubble from './MessageBubble';
import ProgressIndicator from './ProgressIndicator';
import { MemoryInsights } from './MemoryInsights';
import { FileNotification } from './FileNotification';
import { useSettings } from '@/contexts/SettingsContext';
import { useFileWatcher } from '@/hooks/useFileWatcher';
import { cn } from '@/lib/utils';

export type ChatMode = 'simple' | 'enhanced' | 'orchestrator';

interface ChatAreaProps {
  messages: Message[];
  isLoading?: boolean;
  error?: string | null;
  currentMode?: ChatMode;
  progressStatus?: string;
  onEditMessage?: (messageId: string, newContent: string) => void;
  onResendMessage?: (messageId: string, updatedContent?: string) => void;
  onDeleteMessage?: (messageId: string) => void;
  zepSessionId?: string | null;
  showFileNotifications?: boolean;
}

export default function ChatArea({
  messages,
  isLoading = false,
  error,
  currentMode = 'simple',
  progressStatus,
  onEditMessage,
  onResendMessage,
  onDeleteMessage,
  zepSessionId,
  showFileNotifications = true
}: ChatAreaProps) {
  const { settings } = useSettings();
  const router = useRouter();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // File watcher for notifications
  const { newFiles, clearNotifications } = useFileWatcher(showFileNotifications);



  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      const chatContainer = messagesEndRef.current.closest('.overflow-y-auto');
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  if (messages.length === 0 && !isLoading && !error) {
    return (
      <div className="flex-1 flex items-center justify-center bg-background">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-2xl font-bold">N</span>
          </div>
          <h2 className="text-2xl font-bold text-foreground mb-2">
            Welcome to Nexus
          </h2>
          <p className="text-muted-foreground mb-6">
            Your AI-powered chat assistant with enhanced capabilities. Choose your mode and start a conversation.
          </p>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>• <strong>Simple Chat:</strong> Fast AI responses</p>
            <p>• <strong>Enhanced Chat:</strong> AI with tools and web search</p>
            <p>• <strong>Research Mode:</strong> Multi-agent deep analysis</p>
            <p>• Your conversations are saved locally</p>
          </div>
        </div>
      </div>
    );
  }

  // ChatArea component render

  return (
    <div className="h-full flex overflow-hidden bg-background">
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto min-h-full flex flex-col">
            <div className="flex-1">
              {/* File notifications */}
              {newFiles.map((file, index) => (
                <div key={`file-${file.name}-${index}`} className="p-4">
                  <FileNotification
                    fileName={file.name}
                    fileSize={file.size}
                    createdAt={file.created}
                    onViewFiles={() => router.push('/files')}
                    onDismiss={() => {
                      // Remove this specific notification
                      const updatedFiles = newFiles.filter((_, i) => i !== index);
                      if (updatedFiles.length === 0) {
                        clearNotifications();
                      }
                    }}
                  />
                </div>
              ))}
              
              {messages.map((message) => {
                // For enhanced/orchestrator modes, hide empty streaming assistant messages
                // to avoid showing duplicate "Nexus" entries (one empty + one progress indicator)
                const shouldHideEmptyStreaming =
                  message.role === 'assistant' &&
                  message.isStreaming &&
                  message.content === '' &&
                  (currentMode === 'enhanced' || currentMode === 'orchestrator') &&
                  isLoading;

                if (shouldHideEmptyStreaming) {
                  return null;
                }

                return (
                  <MessageBubble
                    key={`${message.id}-${message.timestamp}`}
                    message={message}
                    onEditMessage={onEditMessage}
                    onResendMessage={onResendMessage}
                    onDeleteMessage={onDeleteMessage}
                  />
                );
              }).filter(Boolean)}

            {/* Enhanced progress indicator for enhanced and orchestrator modes */}
            {isLoading && (currentMode === 'enhanced' || currentMode === 'orchestrator') && (
              <ProgressIndicator
                isVisible={true}
                currentStatus={progressStatus}
                mode={currentMode === 'orchestrator' ? 'orchestrator' : 'enhanced'}
              />
            )}



            {/* Enhanced error handling */}
            {error && (
              <div className="p-4 mx-4 mb-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">!</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="text-red-800 font-medium">Error</p>
                        {currentMode !== 'simple' && (
                          <span className="text-xs text-red-600 bg-red-100 px-2 py-0.5 rounded-full">
                            {currentMode === 'orchestrator' ? 'Research Mode' : 'Enhanced Mode'}
                          </span>
                        )}
                      </div>
                      <p className="text-red-600 text-sm mb-2">{error}</p>

                      {/* Show fallback info for enhanced modes */}
                      {currentMode !== 'simple' && error.includes('unavailable') && (
                        <div className="text-xs text-red-500 bg-red-100 p-2 rounded border-l-2 border-red-300">
                          <p className="font-medium mb-1">💡 Suggestion:</p>
                          <p>Try switching to Simple Chat mode for basic AI responses, or check if the enhanced service is running.</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          </div>
        </div>
      </div>
      
      {/* Zep Memory Insights Panel - Show when enabled in settings and enhanced/orchestrator mode is active */}
      {settings.zepSettings.enabled && 
       settings.zepSettings.showMemoryInsights && 
       (currentMode === 'enhanced' || currentMode === 'orchestrator') && 
       zepSessionId && (
        <div className="w-80 border-l border-gray-200 bg-gray-50 overflow-y-auto">
          <div className="p-4">
            <MemoryInsights sessionId={zepSessionId} />
          </div>
        </div>
      )}
    </div>
  );
}
