# Nexus AI Chat - Docker Setup

This document explains how to run the Nexus AI Chat application using Docker.

## Prerequisites

- Docker Desktop installed and running
- Windows 10/11 (for the provided batch scripts)

## Quick Start

### Option 1: One-Click Startup (Recommended)

1. **Create Windows Shortcuts:**
   ```powershell
   # Run as Administrator
   PowerShell -ExecutionPolicy Bypass -File create-shortcut.ps1
   ```

2. **Start the Application:**
   - Double-click the "Nexus AI Chat" shortcut on your desktop, OR
   - Search for "Nexus AI Chat" in the Start Menu

### Option 2: Manual Docker Commands

1. **Start the application:**
   ```bash
   docker-compose up --build -d
   ```

2. **Stop the application:**
   ```bash
   docker-compose down
   ```

3. **View logs:**
   ```bash
   docker-compose logs -f
   ```

## Services

The Docker setup includes three services:

### 1. nexus-db (PostgreSQL Database)
- **Port:** 5433 (external) → 5432 (internal)
- **Database:** ai_chat_app
- **User:** postgres
- **Password:** postgres

### 2. nexus-app (Next.js Application)
- **Port:** 3000
- **URL:** http://localhost:3000
- **Includes:** Frontend + Backend API

### 3. nexus-ai-service (Python AI Service)
- **Port:** 8000
- **URL:** http://localhost:8000
- **Purpose:** Future AI processing capabilities

## Network

All services run on the `nexus-network` bridge network, allowing them to communicate with each other using service names.

## Data Persistence

- Database data is persisted in the `nexus_db_data` Docker volume
- Your conversations and workspaces will be preserved between restarts

## Troubleshooting

### Docker not running
```
Error: Docker is not running. Please start Docker Desktop first.
```
**Solution:** Start Docker Desktop and wait for it to fully initialize.

### Port conflicts
If ports 3000, 5433, or 8000 are already in use:
1. Stop the conflicting services
2. Or modify the ports in `docker-compose.yml`

### Database connection issues
- Ensure the database service is healthy: `docker-compose ps`
- Check logs: `docker-compose logs nexus-db`

### Application not loading
- Wait 30-60 seconds for all services to start
- Check service health: `docker-compose ps`
- View application logs: `docker-compose logs nexus-app`

## Development

To make changes to the application:

1. **Stop the containers:**
   ```bash
   docker-compose down
   ```

2. **Make your changes to the source code**

3. **Rebuild and restart:**
   ```bash
   docker-compose up --build -d
   ```

## Cleanup

To completely remove all containers, volumes, and networks:

```bash
# Stop and remove containers
docker-compose down

# Remove volumes (WARNING: This will delete all data)
docker-compose down -v

# Remove unused Docker resources
docker system prune -a
```

## File Structure

```
ai-chat-app/
├── docker-compose.yml      # Docker services configuration
├── Dockerfile             # Next.js app container definition
├── start-nexus.bat        # Windows startup script
├── create-shortcut.ps1    # Windows shortcut creation
├── .dockerignore          # Docker build exclusions
├── .env.docker           # Docker environment variables
└── DOCKER-README.md      # This file
```
