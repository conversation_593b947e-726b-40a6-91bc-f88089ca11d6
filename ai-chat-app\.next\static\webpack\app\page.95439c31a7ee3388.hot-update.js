"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useEnhancedChat.ts":
/*!**************************************!*\
  !*** ./src/hooks/useEnhancedChat.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEnhancedChat: () => (/* binding */ useEnhancedChat)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/storage */ \"(app-pages-browser)/./src/lib/storage.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_enhanced_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/enhanced-api */ \"(app-pages-browser)/./src/lib/enhanced-api.ts\");\n/* harmony import */ var _lib_zep_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/zep-service */ \"(app-pages-browser)/./src/lib/zep-service.ts\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(app-pages-browser)/./src/contexts/SettingsContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useEnhancedChat auto */ \n\n\n\n\n\n\nfunction useEnhancedChat() {\n    const { settings } = (0,_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_5__.useSettings)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        conversations: [],\n        currentConversationId: null,\n        isLoading: false,\n        error: null,\n        sidebarOpen: false,\n        currentMode: 'simple',\n        progressStatus: undefined,\n        serviceHealth: null\n    });\n    // Initialize storage service, load conversations and check service health on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedChat.useEffect\": ()=>{\n            const initializeStorage = {\n                \"useEnhancedChat.useEffect.initializeStorage\": async ()=>{\n                    try {\n                        // Initialize the storage service\n                        await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.initialize();\n                        // Load conversations and current conversation ID\n                        const conversations = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.loadConversations();\n                        const currentConversationId = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.loadCurrentConversationId();\n                        setState({\n                            \"useEnhancedChat.useEffect.initializeStorage\": (prev)=>({\n                                    ...prev,\n                                    conversations,\n                                    currentConversationId: conversations.find({\n                                        \"useEnhancedChat.useEffect.initializeStorage\": (c)=>c.id === currentConversationId\n                                    }[\"useEnhancedChat.useEffect.initializeStorage\"]) ? currentConversationId : null\n                                })\n                        }[\"useEnhancedChat.useEffect.initializeStorage\"]);\n                        // Check service health after initialization\n                        checkServiceHealth();\n                    } catch (error) {\n                        console.error('Failed to initialize storage:', error);\n                        // Fallback to basic localStorage loading\n                        const conversations = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.loadConversations();\n                        const currentConversationId = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.loadCurrentConversationId();\n                        setState({\n                            \"useEnhancedChat.useEffect.initializeStorage\": (prev)=>({\n                                    ...prev,\n                                    conversations,\n                                    currentConversationId: conversations.find({\n                                        \"useEnhancedChat.useEffect.initializeStorage\": (c)=>c.id === currentConversationId\n                                    }[\"useEnhancedChat.useEffect.initializeStorage\"]) ? currentConversationId : null\n                                })\n                        }[\"useEnhancedChat.useEffect.initializeStorage\"]);\n                        // Still check service health\n                        checkServiceHealth();\n                    }\n                }\n            }[\"useEnhancedChat.useEffect.initializeStorage\"];\n            initializeStorage();\n            // Cleanup on unmount\n            return ({\n                \"useEnhancedChat.useEffect\": ()=>{\n                // No cleanup needed for local storage\n                }\n            })[\"useEnhancedChat.useEffect\"];\n        }\n    }[\"useEnhancedChat.useEffect\"], []);\n    // Add periodic save and beforeunload save\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useEnhancedChat.useEffect\": ()=>{\n            // Save conversations periodically (every 30 seconds)\n            const saveInterval = setInterval({\n                \"useEnhancedChat.useEffect.saveInterval\": async ()=>{\n                    try {\n                        if (state.conversations.length > 0) {\n                            console.log('Periodic save: Saving conversations to database');\n                            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.saveConversations(state.conversations);\n                            console.log('Periodic save: Successfully saved conversations');\n                        }\n                    } catch (error) {\n                        console.error('Periodic save failed:', error);\n                    }\n                }\n            }[\"useEnhancedChat.useEffect.saveInterval\"], 30000);\n            // Save on page unload\n            const handleBeforeUnload = {\n                \"useEnhancedChat.useEffect.handleBeforeUnload\": async ()=>{\n                    try {\n                        if (state.conversations.length > 0) {\n                            console.log('Before unload: Saving conversations to database');\n                            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.saveConversations(state.conversations);\n                            console.log('Before unload: Successfully saved conversations');\n                        }\n                    } catch (error) {\n                        console.error('Before unload save failed:', error);\n                    }\n                }\n            }[\"useEnhancedChat.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"useEnhancedChat.useEffect\": ()=>{\n                    clearInterval(saveInterval);\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"useEnhancedChat.useEffect\"];\n        }\n    }[\"useEnhancedChat.useEffect\"], [\n        state.conversations\n    ]);\n    const checkServiceHealth = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[checkServiceHealth]\": async ()=>{\n            try {\n                console.log('useEnhancedChat: Checking service health...');\n                const health = await _lib_enhanced_api__WEBPACK_IMPORTED_MODULE_3__.EnhancedAPIService.checkServiceHealth();\n                console.log('useEnhancedChat: Service health received:', health);\n                setState({\n                    \"useEnhancedChat.useCallback[checkServiceHealth]\": (prev)=>({\n                            ...prev,\n                            serviceHealth: health\n                        })\n                }[\"useEnhancedChat.useCallback[checkServiceHealth]\"]);\n            } catch (error) {\n                console.error('useEnhancedChat: Failed to check service health:', error);\n                setState({\n                    \"useEnhancedChat.useCallback[checkServiceHealth]\": (prev)=>({\n                            ...prev,\n                            serviceHealth: null\n                        })\n                }[\"useEnhancedChat.useCallback[checkServiceHealth]\"]);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[checkServiceHealth]\"], []);\n    const getCurrentConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[getCurrentConversation]\": ()=>{\n            if (!state.currentConversationId) {\n                return null;\n            }\n            const found = state.conversations.find({\n                \"useEnhancedChat.useCallback[getCurrentConversation]\": (c)=>c.id === state.currentConversationId\n            }[\"useEnhancedChat.useCallback[getCurrentConversation]\"]) || null;\n            return found;\n        }\n    }[\"useEnhancedChat.useCallback[getCurrentConversation]\"], [\n        state.conversations,\n        state.currentConversationId\n    ]);\n    const createNewConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[createNewConversation]\": async (title, userId)=>{\n            const conversationId = (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n            let zepSessionId;\n            // Create Zep session if service is configured and userId is provided\n            if (userId) {\n                try {\n                    const zepService = _lib_zep_service__WEBPACK_IMPORTED_MODULE_4__.ZepService.getInstance();\n                    if (zepService.isConfigured()) {\n                        zepSessionId = await zepService.addSession(conversationId, userId);\n                        console.log('Created Zep session:', zepSessionId);\n                    }\n                } catch (error) {\n                    console.warn('Failed to create Zep session:', error);\n                }\n            }\n            const newConversation = {\n                id: conversationId,\n                title: title || 'New Chat',\n                messages: [],\n                createdAt: Date.now(),\n                updatedAt: Date.now(),\n                userId,\n                zepSessionId,\n                facts: [],\n                entities: []\n            };\n            setState({\n                \"useEnhancedChat.useCallback[createNewConversation]\": (prev)=>({\n                        ...prev,\n                        conversations: [\n                            newConversation,\n                            ...prev.conversations\n                        ],\n                        currentConversationId: newConversation.id,\n                        error: null\n                    })\n            }[\"useEnhancedChat.useCallback[createNewConversation]\"]);\n            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(newConversation);\n            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.saveCurrentConversationId(newConversation.id);\n            return newConversation.id;\n        }\n    }[\"useEnhancedChat.useCallback[createNewConversation]\"], []);\n    const selectConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[selectConversation]\": async (id)=>{\n            setState({\n                \"useEnhancedChat.useCallback[selectConversation]\": (prev)=>({\n                        ...prev,\n                        currentConversationId: id,\n                        error: null\n                    })\n            }[\"useEnhancedChat.useCallback[selectConversation]\"]);\n            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.saveCurrentConversationId(id);\n        }\n    }[\"useEnhancedChat.useCallback[selectConversation]\"], []);\n    const deleteConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[deleteConversation]\": async (id)=>{\n            setState({\n                \"useEnhancedChat.useCallback[deleteConversation]\": (prev)=>{\n                    const newConversations = prev.conversations.filter({\n                        \"useEnhancedChat.useCallback[deleteConversation].newConversations\": (c)=>c.id !== id\n                    }[\"useEnhancedChat.useCallback[deleteConversation].newConversations\"]);\n                    const newCurrentId = prev.currentConversationId === id ? newConversations.length > 0 ? newConversations[0].id : null : prev.currentConversationId;\n                    return {\n                        ...prev,\n                        conversations: newConversations,\n                        currentConversationId: newCurrentId,\n                        error: null\n                    };\n                }\n            }[\"useEnhancedChat.useCallback[deleteConversation]\"]);\n            await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.deleteConversation(id);\n        }\n    }[\"useEnhancedChat.useCallback[deleteConversation]\"], []);\n    const renameConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[renameConversation]\": async (id, newTitle)=>{\n            // Update local state immediately for responsive UI\n            setState({\n                \"useEnhancedChat.useCallback[renameConversation]\": (prev)=>({\n                        ...prev,\n                        conversations: prev.conversations.map({\n                            \"useEnhancedChat.useCallback[renameConversation]\": (c)=>c.id === id ? {\n                                    ...c,\n                                    title: newTitle,\n                                    updatedAt: Date.now()\n                                } : c\n                        }[\"useEnhancedChat.useCallback[renameConversation]\"])\n                    })\n            }[\"useEnhancedChat.useCallback[renameConversation]\"]);\n            try {\n                // Use the new dedicated rename method\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.renameConversation(id, newTitle);\n            } catch (error) {\n                console.error('Failed to rename conversation:', error);\n                // Revert the local state change on error\n                setState({\n                    \"useEnhancedChat.useCallback[renameConversation]\": (prev)=>({\n                            ...prev,\n                            conversations: prev.conversations.map({\n                                \"useEnhancedChat.useCallback[renameConversation]\": (c)=>{\n                                    var _state_conversations_find;\n                                    return c.id === id ? {\n                                        ...c,\n                                        title: ((_state_conversations_find = state.conversations.find({\n                                            \"useEnhancedChat.useCallback[renameConversation]\": (conv)=>conv.id === id\n                                        }[\"useEnhancedChat.useCallback[renameConversation]\"])) === null || _state_conversations_find === void 0 ? void 0 : _state_conversations_find.title) || 'New Chat'\n                                    } : c;\n                                }\n                            }[\"useEnhancedChat.useCallback[renameConversation]\"])\n                        })\n                }[\"useEnhancedChat.useCallback[renameConversation]\"]);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[renameConversation]\"], [\n        state.conversations\n    ]);\n    const setMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[setMode]\": (mode)=>{\n            console.log('🔄 MODE CHANGE: Setting mode to:', mode);\n            setState({\n                \"useEnhancedChat.useCallback[setMode]\": (prev)=>({\n                        ...prev,\n                        currentMode: mode\n                    })\n            }[\"useEnhancedChat.useCallback[setMode]\"]);\n        }\n    }[\"useEnhancedChat.useCallback[setMode]\"], []);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[sendMessage]\": async function(content, config) {\n            let isResend = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n            console.log('sendMessage called with content:', content, 'isResend:', isResend);\n            if (!content.trim()) {\n                console.log('Content is empty, returning');\n                return;\n            }\n            // Get current state values directly from state\n            let conversationId = state.currentConversationId;\n            let currentConversations = state.conversations;\n            let currentMode = state.currentMode;\n            // Create new conversation if none exists\n            if (!conversationId) {\n                const newConversation = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                    title: content.slice(0, 50),\n                    messages: [],\n                    createdAt: Date.now(),\n                    updatedAt: Date.now()\n                };\n                console.log('Creating new conversation:', {\n                    newConversationId: newConversation.id,\n                    title: newConversation.title,\n                    prevConversationsCount: currentConversations.length\n                });\n                conversationId = newConversation.id;\n                currentConversations = [\n                    newConversation,\n                    ...currentConversations\n                ];\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(newConversation);\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.saveCurrentConversationId(newConversation.id);\n                console.log('New conversation created and saved:', {\n                    conversationId,\n                    newConversationsCount: currentConversations.length,\n                    conversationIds: currentConversations.map({\n                        \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                });\n                setState({\n                    \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>({\n                            ...prev,\n                            conversations: currentConversations,\n                            currentConversationId: conversationId,\n                            error: null\n                        })\n                }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n            }\n            console.log('🚀 SEND MESSAGE called with mode:', {\n                currentMode,\n                content: content.substring(0, 50),\n                config,\n                conversationId\n            });\n            // For resend operations, get the latest conversation state to ensure we have updated content\n            let conversation;\n            if (isResend) {\n                var _conversation_messages;\n                conversation = getCurrentConversation();\n                console.log('useEnhancedChat: Getting latest conversation for resend:', {\n                    conversationId,\n                    found: !!conversation,\n                    messageCount: (conversation === null || conversation === void 0 ? void 0 : (_conversation_messages = conversation.messages) === null || _conversation_messages === void 0 ? void 0 : _conversation_messages.length) || 0\n                });\n            } else {\n                conversation = currentConversations.find({\n                    \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId\n                }[\"useEnhancedChat.useCallback[sendMessage]\"]) || null;\n                console.log('useEnhancedChat: Looking for conversation:', {\n                    conversationId,\n                    availableConversations: currentConversations.map({\n                        \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"]),\n                    found: !!conversation\n                });\n            }\n            if (!conversation) {\n                console.error('Conversation not found:', conversationId);\n                return;\n            }\n            let userMessage;\n            let messagesToAdd;\n            if (isResend) {\n                // For resend, we assume the user message is already updated in the conversation\n                // We only need to add a new assistant message\n                const assistantMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                    role: 'assistant',\n                    content: '',\n                    timestamp: Date.now(),\n                    isStreaming: true\n                };\n                // Use the content parameter for the user message (this contains the updated content)\n                // Get the last user message from conversation for metadata (id, timestamp, etc.)\n                const lastUserMessage = conversation.messages.filter({\n                    \"useEnhancedChat.useCallback[sendMessage].lastUserMessage\": (m)=>m.role === 'user'\n                }[\"useEnhancedChat.useCallback[sendMessage].lastUserMessage\"]).pop();\n                if (!lastUserMessage) {\n                    console.error('No user message found for resend');\n                    return;\n                }\n                // Create userMessage with updated content but preserve other properties\n                userMessage = {\n                    ...lastUserMessage,\n                    content: content.trim() // Use the updated content passed to sendMessage\n                };\n                messagesToAdd = [\n                    assistantMessage\n                ];\n            } else {\n                // Normal send: create both user and assistant messages\n                userMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                    role: 'user',\n                    content: content.trim(),\n                    timestamp: Date.now()\n                };\n                const assistantMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(),\n                    role: 'assistant',\n                    content: '',\n                    timestamp: Date.now(),\n                    isStreaming: true\n                };\n                messagesToAdd = [\n                    userMessage,\n                    assistantMessage\n                ];\n            }\n            // Add messages to conversation\n            const updatedConversation = {\n                ...conversation,\n                messages: [\n                    ...conversation.messages,\n                    ...messagesToAdd\n                ],\n                updatedAt: Date.now(),\n                title: conversation.messages.length === 0 ? content.slice(0, 50) : conversation.title\n            };\n            // Get the assistant message for later reference\n            const assistantMessage = messagesToAdd.find({\n                \"useEnhancedChat.useCallback[sendMessage].assistantMessage\": (m)=>m.role === 'assistant'\n            }[\"useEnhancedChat.useCallback[sendMessage].assistantMessage\"]);\n            if (!assistantMessage) {\n                console.error('No assistant message found in messagesToAdd');\n                return;\n            }\n            // Update state and save to storage immediately\n            setState({\n                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                    var _userMessage_content, _newState_conversations_find;\n                    console.log('setState: Adding messages to conversation', {\n                        conversationId,\n                        prevConversationsCount: prev.conversations.length,\n                        updatedConversationMessagesCount: updatedConversation.messages.length,\n                        userMessage: {\n                            id: userMessage.id,\n                            role: userMessage.role,\n                            content: (_userMessage_content = userMessage.content) === null || _userMessage_content === void 0 ? void 0 : _userMessage_content.substring(0, 50)\n                        },\n                        assistantMessage: {\n                            id: assistantMessage.id,\n                            role: assistantMessage.role,\n                            content: assistantMessage.content\n                        },\n                        isResend\n                    });\n                    const newState = {\n                        ...prev,\n                        conversations: prev.conversations.map({\n                            \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId ? updatedConversation : c\n                        }[\"useEnhancedChat.useCallback[sendMessage]\"]),\n                        currentConversationId: conversationId,\n                        isLoading: true,\n                        error: null\n                    };\n                    console.log('setState: New state after adding messages', {\n                        conversationsCount: newState.conversations.length,\n                        currentConversationId: newState.currentConversationId,\n                        currentConversation: (_newState_conversations_find = newState.conversations.find({\n                            \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId\n                        }[\"useEnhancedChat.useCallback[sendMessage]\"])) === null || _newState_conversations_find === void 0 ? void 0 : _newState_conversations_find.messages.map({\n                            \"useEnhancedChat.useCallback[sendMessage]\": (m)=>{\n                                var _m_content;\n                                return {\n                                    id: m.id,\n                                    role: m.role,\n                                    contentLength: (_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length\n                                };\n                            }\n                        }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                    });\n                    return newState;\n                }\n            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n            // Save updated conversation to storage with error handling\n            try {\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n            } catch (storageError) {\n                console.warn('Failed to save conversation to storage during sendMessage, but continuing:', storageError);\n            // Don't throw the error - the message was added to state successfully, so continue\n            }\n            // Messages added to state\n            try {\n                const messages = [\n                    ...conversation.messages,\n                    userMessage\n                ].map({\n                    \"useEnhancedChat.useCallback[sendMessage].messages\": (msg)=>({\n                            role: msg.role,\n                            content: msg.content\n                        })\n                }[\"useEnhancedChat.useCallback[sendMessage].messages\"]);\n                let fullResponse = '';\n                console.log(\"\\uD83D\\uDE80 SEND MESSAGE called with mode: \".concat(currentMode));\n                // Use appropriate API based on mode\n                if (currentMode === 'simple') {\n                    // Use original API service for simple mode\n                    const modelToUse = settings.preferredModels.simple;\n                    const maxTokens = settings.responseSettings.streaming.simple;\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_2__.APIService.sendMessage(messages, {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onChunk\n                        (chunk)=>{\n                            fullResponse += chunk;\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                    const newState = {\n                                        ...prev,\n                                        conversations: prev.conversations.map({\n                                            \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId ? {\n                                                    ...c,\n                                                    messages: c.messages.map({\n                                                        \"useEnhancedChat.useCallback[sendMessage]\": (m)=>m.id === assistantMessage.id ? {\n                                                                ...m,\n                                                                content: fullResponse\n                                                            } : m\n                                                    }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                                                } : c\n                                        }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                                    };\n                                    return newState;\n                                }\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onComplete\n                        async ()=>{\n                            console.log('Simple mode message completed');\n                            // Check if this is the first user message (conversation had no messages before)\n                            const isFirstMessage = conversation.messages.length === 0;\n                            let finalConversationForZep;\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                    const updatedConversations = prev.conversations.map({\n                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                ...c,\n                                                messages: c.messages.map({\n                                                    \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (m)=>m.id === assistantMessage.id ? {\n                                                            ...m,\n                                                            content: fullResponse,\n                                                            isStreaming: false\n                                                        } : m\n                                                }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]),\n                                                updatedAt: Date.now()\n                                            } : c\n                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                    finalConversationForZep = updatedConversations.find({\n                                        \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId\n                                    }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                                    return {\n                                        ...prev,\n                                        conversations: updatedConversations,\n                                        isLoading: false\n                                    };\n                                }\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                            // Save to storage with error handling\n                            if (finalConversationForZep) {\n                                try {\n                                    var _finalConversationForZep_messages__content, _finalConversationForZep_messages_;\n                                    console.log(\"Saving conversation after \".concat(currentMode, \" mode completion:\"), {\n                                        conversationId: finalConversationForZep.id,\n                                        messageCount: finalConversationForZep.messages.length,\n                                        lastMessageContent: (_finalConversationForZep_messages_ = finalConversationForZep.messages[finalConversationForZep.messages.length - 1]) === null || _finalConversationForZep_messages_ === void 0 ? void 0 : (_finalConversationForZep_messages__content = _finalConversationForZep_messages_.content) === null || _finalConversationForZep_messages__content === void 0 ? void 0 : _finalConversationForZep_messages__content.substring(0, 100)\n                                    });\n                                    await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(finalConversationForZep);\n                                    console.log(\"Successfully saved conversation after \".concat(currentMode, \" mode completion\"));\n                                } catch (error) {\n                                    console.error(\"Failed to save conversation after \".concat(currentMode, \" mode completion:\"), error);\n                                    // Try to save again after a short delay\n                                    setTimeout({\n                                        \"useEnhancedChat.useCallback[sendMessage]\": async ()=>{\n                                            try {\n                                                console.log(\"Retrying conversation save after \".concat(currentMode, \" mode completion\"));\n                                                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(finalConversationForZep);\n                                                console.log(\"Successfully saved conversation on retry after \".concat(currentMode, \" mode completion\"));\n                                            } catch (retryError) {\n                                                console.error(\"Failed to save conversation on retry after \".concat(currentMode, \" mode completion:\"), retryError);\n                                            }\n                                        }\n                                    }[\"useEnhancedChat.useCallback[sendMessage]\"], 1000);\n                                }\n                            }\n                            // Generate title for first message (only for queries with more than 6 words)\n                            if (isFirstMessage && finalConversationForZep) {\n                                const wordCount = content.trim().split(/\\s+/).length;\n                                if (wordCount > 6) {\n                                    try {\n                                        var _settings_preferredModels;\n                                        const response = await fetch('/api/generate-title', {\n                                            method: 'POST',\n                                            headers: {\n                                                'Content-Type': 'application/json'\n                                            },\n                                            body: JSON.stringify({\n                                                userMessage: content.trim(),\n                                                model: (_settings_preferredModels = settings.preferredModels) === null || _settings_preferredModels === void 0 ? void 0 : _settings_preferredModels.titleGeneration\n                                            })\n                                        });\n                                        if (response.ok) {\n                                            const data = await response.json();\n                                            const generatedTitle = data.title;\n                                            // Update conversation with generated title\n                                            setState({\n                                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                                    const updatedConversations = prev.conversations.map({\n                                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                                ...c,\n                                                                title: generatedTitle,\n                                                                updatedAt: Date.now()\n                                                            } : c\n                                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                                    // Save updated conversation to storage\n                                                    const updatedConversation = updatedConversations.find({\n                                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversation\": (c)=>c.id === conversationId\n                                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversation\"]);\n                                                    if (updatedConversation) {\n                                                        _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n                                                    }\n                                                    return {\n                                                        ...prev,\n                                                        conversations: updatedConversations\n                                                    };\n                                                }\n                                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                                        }\n                                    } catch (error) {\n                                        console.error('Failed to generate title:', error);\n                                    // Title generation failure is not critical, continue without it\n                                    }\n                                } else {\n                                    console.log(\"Skipping title generation: query has \".concat(wordCount, \" words (need >6)\"));\n                                }\n                            }\n                            // Send messages to Zep if configured\n                            if (finalConversationForZep === null || finalConversationForZep === void 0 ? void 0 : finalConversationForZep.zepSessionId) {\n                                try {\n                                    const zepService = _lib_zep_service__WEBPACK_IMPORTED_MODULE_4__.ZepService.getInstance();\n                                    if (zepService.isConfigured()) {\n                                        await zepService.addMessages(finalConversationForZep.zepSessionId, [\n                                            {\n                                                role: 'user',\n                                                content: userMessage.content\n                                            },\n                                            {\n                                                role: 'assistant',\n                                                content: fullResponse\n                                            }\n                                        ]);\n                                        console.log('Messages sent to Zep successfully');\n                                    }\n                                } catch (error) {\n                                    console.warn('Failed to send messages to Zep:', error);\n                                }\n                            }\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onError\n                        (error)=>{\n                            console.error('Simple mode API error:', error);\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>({\n                                        ...prev,\n                                        conversations: prev.conversations.map({\n                                            \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId ? {\n                                                    ...c,\n                                                    messages: c.messages.filter({\n                                                        \"useEnhancedChat.useCallback[sendMessage]\": (m)=>m.id !== assistantMessage.id\n                                                    }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                                                } : c\n                                        }[\"useEnhancedChat.useCallback[sendMessage]\"]),\n                                        isLoading: false,\n                                        error,\n                                        progressStatus: undefined\n                                    })\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], // model\n                    modelToUse);\n                } else {\n                    // Use enhanced API service for enhanced and orchestrator modes\n                    const enhancedConfig = {\n                        mode: currentMode === 'orchestrator' ? 'orchestrator' : 'single',\n                        maxIterations: (config === null || config === void 0 ? void 0 : config.maxIterations) || 3,\n                        parallelAgents: (config === null || config === void 0 ? void 0 : config.parallelAgents) || 3,\n                        timeout: (config === null || config === void 0 ? void 0 : config.timeout) || 300000,\n                        // Force tools to be enabled when in enhanced mode (user clicked Tools button)\n                        toolsEnabled: currentMode === 'enhanced' ? true : (config === null || config === void 0 ? void 0 : config.toolsEnabled) !== false\n                    };\n                    console.log(\"\\uD83D\\uDD27 Enhanced mode config:\", {\n                        mode: enhancedConfig.mode,\n                        toolsEnabled: enhancedConfig.toolsEnabled,\n                        currentMode,\n                        toolsForced: currentMode === 'enhanced'\n                    });\n                    await _lib_enhanced_api__WEBPACK_IMPORTED_MODULE_3__.EnhancedAPIService.sendEnhancedMessage(messages, enhancedConfig, {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onChunk\n                        (chunk)=>{\n                            fullResponse += chunk;\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                    const updatedConversations = prev.conversations.map({\n                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                ...c,\n                                                messages: c.messages.map({\n                                                    \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (m)=>m.id === assistantMessage.id ? {\n                                                            ...m,\n                                                            content: fullResponse\n                                                        } : m\n                                                }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"])\n                                            } : c\n                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                    return {\n                                        ...prev,\n                                        conversations: updatedConversations,\n                                        currentConversationId: conversationId // Maintain conversation ID during streaming\n                                    };\n                                }\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onComplete\n                        async ()=>{\n                            console.log(\"\".concat(currentMode, \" mode message completed\"));\n                            // Check if this is the first user message (conversation had no messages before)\n                            const isFirstMessage = conversation.messages.length === 0;\n                            let finalConversationForZep;\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                    const updatedConversations = prev.conversations.map({\n                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                ...c,\n                                                messages: c.messages.map({\n                                                    \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (m)=>m.id === assistantMessage.id ? {\n                                                            ...m,\n                                                            content: fullResponse,\n                                                            isStreaming: false\n                                                        } : m\n                                                }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]),\n                                                updatedAt: Date.now()\n                                            } : c\n                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                    finalConversationForZep = updatedConversations.find({\n                                        \"useEnhancedChat.useCallback[sendMessage]\": (c)=>c.id === conversationId\n                                    }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                                    return {\n                                        ...prev,\n                                        conversations: updatedConversations,\n                                        isLoading: false\n                                    };\n                                }\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                            // Save to storage with error handling\n                            if (finalConversationForZep) {\n                                try {\n                                    var _finalConversationForZep_messages__content, _finalConversationForZep_messages_;\n                                    console.log(\"Saving conversation after \".concat(currentMode, \" mode completion:\"), {\n                                        conversationId: finalConversationForZep.id,\n                                        messageCount: finalConversationForZep.messages.length,\n                                        lastMessageContent: (_finalConversationForZep_messages_ = finalConversationForZep.messages[finalConversationForZep.messages.length - 1]) === null || _finalConversationForZep_messages_ === void 0 ? void 0 : (_finalConversationForZep_messages__content = _finalConversationForZep_messages_.content) === null || _finalConversationForZep_messages__content === void 0 ? void 0 : _finalConversationForZep_messages__content.substring(0, 100)\n                                    });\n                                    await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(finalConversationForZep);\n                                    console.log(\"Successfully saved conversation after \".concat(currentMode, \" mode completion\"));\n                                } catch (error) {\n                                    console.error(\"Failed to save conversation after \".concat(currentMode, \" mode completion:\"), error);\n                                    // Try to save again after a short delay\n                                    setTimeout({\n                                        \"useEnhancedChat.useCallback[sendMessage]\": async ()=>{\n                                            try {\n                                                console.log(\"Retrying conversation save after \".concat(currentMode, \" mode completion\"));\n                                                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(finalConversationForZep);\n                                                console.log(\"Successfully saved conversation on retry after \".concat(currentMode, \" mode completion\"));\n                                            } catch (retryError) {\n                                                console.error(\"Failed to save conversation on retry after \".concat(currentMode, \" mode completion:\"), retryError);\n                                            }\n                                        }\n                                    }[\"useEnhancedChat.useCallback[sendMessage]\"], 1000);\n                                }\n                            }\n                            // Generate title for first message (only for queries with more than 6 words)\n                            if (isFirstMessage && finalConversationForZep) {\n                                const wordCount = content.trim().split(/\\s+/).length;\n                                if (wordCount > 6) {\n                                    try {\n                                        var _settings_preferredModels;\n                                        const response = await fetch('/api/generate-title', {\n                                            method: 'POST',\n                                            headers: {\n                                                'Content-Type': 'application/json'\n                                            },\n                                            body: JSON.stringify({\n                                                userMessage: content.trim(),\n                                                model: (_settings_preferredModels = settings.preferredModels) === null || _settings_preferredModels === void 0 ? void 0 : _settings_preferredModels.titleGeneration\n                                            })\n                                        });\n                                        if (response.ok) {\n                                            const data = await response.json();\n                                            const generatedTitle = data.title;\n                                            // Update conversation with generated title\n                                            setState({\n                                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                                    const updatedConversations = prev.conversations.map({\n                                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                                ...c,\n                                                                title: generatedTitle,\n                                                                updatedAt: Date.now()\n                                                            } : c\n                                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                                    // Save updated conversation to storage\n                                                    const updatedConversation = updatedConversations.find({\n                                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversation\": (c)=>c.id === conversationId\n                                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversation\"]);\n                                                    if (updatedConversation) {\n                                                        _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n                                                    }\n                                                    return {\n                                                        ...prev,\n                                                        conversations: updatedConversations\n                                                    };\n                                                }\n                                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                                        }\n                                    } catch (error) {\n                                        console.error('Failed to generate title:', error);\n                                    // Title generation failure is not critical, continue without it\n                                    }\n                                } else {\n                                    console.log(\"Skipping title generation: query has \".concat(wordCount, \" words (need >6)\"));\n                                }\n                            }\n                            // Send messages to Zep if configured\n                            if (finalConversationForZep === null || finalConversationForZep === void 0 ? void 0 : finalConversationForZep.zepSessionId) {\n                                try {\n                                    const zepService = _lib_zep_service__WEBPACK_IMPORTED_MODULE_4__.ZepService.getInstance();\n                                    if (zepService.isConfigured()) {\n                                        await zepService.addMessages(finalConversationForZep.zepSessionId, [\n                                            {\n                                                role: 'user',\n                                                content: userMessage.content\n                                            },\n                                            {\n                                                role: 'assistant',\n                                                content: fullResponse\n                                            }\n                                        ]);\n                                        console.log('Messages sent to Zep successfully');\n                                    }\n                                } catch (error) {\n                                    console.warn('Failed to send messages to Zep:', error);\n                                }\n                            }\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onError\n                        (error)=>{\n                            console.error(\"\".concat(currentMode, \" mode API error:\"), error);\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                                    const updatedConversations = prev.conversations.map({\n                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                                ...c,\n                                                messages: c.messages.filter({\n                                                    \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (m)=>m.id !== assistantMessage.id\n                                                }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"])\n                                            } : c\n                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                                    return {\n                                        ...prev,\n                                        conversations: updatedConversations,\n                                        currentConversationId: conversationId,\n                                        isLoading: false,\n                                        error,\n                                        progressStatus: undefined\n                                    };\n                                }\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"], {\n                        \"useEnhancedChat.useCallback[sendMessage]\": // onProgress (for orchestrator mode)\n                        (status)=>{\n                            console.log('Progress update:', status);\n                            setState({\n                                \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>({\n                                        ...prev,\n                                        progressStatus: status\n                                    })\n                            }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                        }\n                    }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n                }\n            } catch (error) {\n                console.error('Send message error (catch block):', error);\n                console.error('catch: Removing assistant message and updating state');\n                setState({\n                    \"useEnhancedChat.useCallback[sendMessage]\": (prev)=>{\n                        const updatedConversations = prev.conversations.map({\n                            \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (c)=>c.id === conversationId ? {\n                                    ...c,\n                                    messages: c.messages.filter({\n                                        \"useEnhancedChat.useCallback[sendMessage].updatedConversations\": (m)=>m.id !== assistantMessage.id\n                                    }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"])\n                                } : c\n                        }[\"useEnhancedChat.useCallback[sendMessage].updatedConversations\"]);\n                        console.error('catch: Updated conversations after error:', updatedConversations.map({\n                            \"useEnhancedChat.useCallback[sendMessage]\": (c)=>({\n                                    id: c.id,\n                                    messageCount: c.messages.length,\n                                    messages: c.messages.map({\n                                        \"useEnhancedChat.useCallback[sendMessage]\": (m)=>{\n                                            var _m_content;\n                                            return {\n                                                id: m.id,\n                                                role: m.role,\n                                                contentLength: ((_m_content = m.content) === null || _m_content === void 0 ? void 0 : _m_content.length) || 0\n                                            };\n                                        }\n                                    }[\"useEnhancedChat.useCallback[sendMessage]\"])\n                                })\n                        }[\"useEnhancedChat.useCallback[sendMessage]\"]));\n                        return {\n                            ...prev,\n                            conversations: updatedConversations,\n                            currentConversationId: conversationId,\n                            isLoading: false,\n                            error: error instanceof Error ? error.message : 'An error occurred',\n                            progressStatus: undefined\n                        };\n                    }\n                }[\"useEnhancedChat.useCallback[sendMessage]\"]);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[sendMessage]\"], [\n        state.currentConversationId,\n        state.conversations,\n        state.currentMode,\n        settings\n    ]);\n    const toggleSidebar = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[toggleSidebar]\": ()=>{\n            setState({\n                \"useEnhancedChat.useCallback[toggleSidebar]\": (prev)=>({\n                        ...prev,\n                        sidebarOpen: !prev.sidebarOpen\n                    })\n            }[\"useEnhancedChat.useCallback[toggleSidebar]\"]);\n        }\n    }[\"useEnhancedChat.useCallback[toggleSidebar]\"], []);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[clearError]\": ()=>{\n            setState({\n                \"useEnhancedChat.useCallback[clearError]\": (prev)=>({\n                        ...prev,\n                        error: null\n                    })\n            }[\"useEnhancedChat.useCallback[clearError]\"]);\n        }\n    }[\"useEnhancedChat.useCallback[clearError]\"], []);\n    const editMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[editMessage]\": async (messageId, newContent)=>{\n            let updatedConversation;\n            setState({\n                \"useEnhancedChat.useCallback[editMessage]\": (prev)=>{\n                    const updatedConversations = prev.conversations.map({\n                        \"useEnhancedChat.useCallback[editMessage].updatedConversations\": (conversation)=>{\n                            if (conversation.id === prev.currentConversationId) {\n                                const updatedMessages = conversation.messages.map({\n                                    \"useEnhancedChat.useCallback[editMessage].updatedConversations.updatedMessages\": (message)=>message.id === messageId ? {\n                                            ...message,\n                                            content: newContent\n                                        } : message\n                                }[\"useEnhancedChat.useCallback[editMessage].updatedConversations.updatedMessages\"]);\n                                updatedConversation = {\n                                    ...conversation,\n                                    messages: updatedMessages,\n                                    updatedAt: Date.now()\n                                };\n                                return updatedConversation;\n                            }\n                            return conversation;\n                        }\n                    }[\"useEnhancedChat.useCallback[editMessage].updatedConversations\"]);\n                    return {\n                        ...prev,\n                        conversations: updatedConversations\n                    };\n                }\n            }[\"useEnhancedChat.useCallback[editMessage]\"]);\n            // Save to storage\n            if (updatedConversation) {\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[editMessage]\"], []);\n    const resendMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[resendMessage]\": async (messageId, updatedContent)=>{\n            console.log('resendMessage called with:', {\n                messageId,\n                updatedContent\n            });\n            const currentConversation = getCurrentConversation();\n            if (!currentConversation) {\n                console.log('No current conversation found');\n                return;\n            }\n            const messageIndex = currentConversation.messages.findIndex({\n                \"useEnhancedChat.useCallback[resendMessage].messageIndex\": (m)=>m.id === messageId\n            }[\"useEnhancedChat.useCallback[resendMessage].messageIndex\"]);\n            if (messageIndex === -1) {\n                console.log('Message not found in conversation');\n                return;\n            }\n            const userMessage = currentConversation.messages[messageIndex];\n            if (userMessage.role !== 'user') {\n                console.log('Message is not from user');\n                return;\n            }\n            // Use the updated content if provided, otherwise use the current message content\n            const contentToSend = updatedContent || userMessage.content;\n            console.log('Content to send:', contentToSend);\n            console.log('Original message content:', userMessage.content);\n            // Remove all messages after the edited user message\n            const messagesToKeep = currentConversation.messages.slice(0, messageIndex + 1);\n            // If we have updated content, update the message in the kept messages\n            if (updatedContent) {\n                messagesToKeep[messageIndex] = {\n                    ...userMessage,\n                    content: updatedContent,\n                    timestamp: Date.now() // Update timestamp to ensure object reference changes\n                };\n            }\n            const updatedConversation = {\n                ...currentConversation,\n                messages: messagesToKeep,\n                updatedAt: Date.now()\n            };\n            // STEP 1: Update the message content in the UI first\n            console.log('STEP 1: Updating message content in UI...');\n            setState({\n                \"useEnhancedChat.useCallback[resendMessage]\": (prev)=>{\n                    var _updatedConversation_messages_find_content, _updatedConversation_messages_find;\n                    const updatedConversations = prev.conversations.map({\n                        \"useEnhancedChat.useCallback[resendMessage].updatedConversations\": (c)=>c.id === currentConversation.id ? updatedConversation : c\n                    }[\"useEnhancedChat.useCallback[resendMessage].updatedConversations\"]);\n                    const newState = {\n                        ...prev,\n                        conversations: updatedConversations\n                    };\n                    console.log('STEP 1 COMPLETE: Message content updated in UI:', {\n                        messageId,\n                        updatedContent,\n                        messageInState: (_updatedConversation_messages_find = updatedConversation.messages.find({\n                            \"useEnhancedChat.useCallback[resendMessage]\": (m)=>m.id === messageId\n                        }[\"useEnhancedChat.useCallback[resendMessage]\"])) === null || _updatedConversation_messages_find === void 0 ? void 0 : (_updatedConversation_messages_find_content = _updatedConversation_messages_find.content) === null || _updatedConversation_messages_find_content === void 0 ? void 0 : _updatedConversation_messages_find_content.substring(0, 100),\n                        conversationId: currentConversation.id,\n                        totalMessages: updatedConversation.messages.length\n                    });\n                    return newState;\n                }\n            }[\"useEnhancedChat.useCallback[resendMessage]\"]);\n            // Save to storage with error handling\n            try {\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n            } catch (storageError) {\n                console.warn('Failed to save conversation to storage, but continuing with resend:', storageError);\n            // Don't throw the error - the UI update was successful, so continue with the resend\n            }\n            // STEP 2: Wait for UI to update, then send the new query\n            console.log('STEP 2: Waiting for UI update, then sending new query...');\n            await new Promise({\n                \"useEnhancedChat.useCallback[resendMessage]\": (resolve)=>setTimeout(resolve, 1000)\n            }[\"useEnhancedChat.useCallback[resendMessage]\"]); // Longer delay to ensure UI updates\n            // Resend the message with the correct content\n            console.log('STEP 2: Sending new query with content:', contentToSend);\n            await sendMessage(contentToSend, undefined, true);\n        }\n    }[\"useEnhancedChat.useCallback[resendMessage]\"], [\n        sendMessage,\n        getCurrentConversation\n    ]);\n    const deleteMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[deleteMessage]\": async (messageId)=>{\n            console.log('deleteMessage called with messageId:', messageId);\n            const currentConversation = getCurrentConversation();\n            if (!currentConversation) {\n                console.log('No current conversation found');\n                return;\n            }\n            const messageIndex = currentConversation.messages.findIndex({\n                \"useEnhancedChat.useCallback[deleteMessage].messageIndex\": (m)=>m.id === messageId\n            }[\"useEnhancedChat.useCallback[deleteMessage].messageIndex\"]);\n            if (messageIndex === -1) {\n                console.log('Message not found in conversation');\n                return;\n            }\n            try {\n                console.log('Calling StorageService.deleteMessage');\n                // Delete the message from the database\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.deleteMessage(currentConversation.id, messageId);\n                console.log('Message deleted from storage successfully');\n                // Update local state after successful deletion\n                const updatedMessages = currentConversation.messages.filter({\n                    \"useEnhancedChat.useCallback[deleteMessage].updatedMessages\": (m)=>m.id !== messageId\n                }[\"useEnhancedChat.useCallback[deleteMessage].updatedMessages\"]);\n                const updatedConversation = {\n                    ...currentConversation,\n                    messages: updatedMessages,\n                    updatedAt: Date.now()\n                };\n                setState({\n                    \"useEnhancedChat.useCallback[deleteMessage]\": (prev)=>{\n                        const updatedConversations = prev.conversations.map({\n                            \"useEnhancedChat.useCallback[deleteMessage].updatedConversations\": (c)=>c.id === currentConversation.id ? updatedConversation : c\n                        }[\"useEnhancedChat.useCallback[deleteMessage].updatedConversations\"]);\n                        return {\n                            ...prev,\n                            conversations: updatedConversations\n                        };\n                    }\n                }[\"useEnhancedChat.useCallback[deleteMessage]\"]);\n            } catch (error) {\n                console.error('Failed to delete message:', error);\n            // Optionally show an error message to the user\n            }\n        }\n    }[\"useEnhancedChat.useCallback[deleteMessage]\"], [\n        getCurrentConversation\n    ]);\n    // Zep helper functions\n    const searchMemory = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[searchMemory]\": async (userId, query, limit)=>{\n            try {\n                const response = await fetch('/api/zep/search', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        userId,\n                        query,\n                        limit: limit || 10\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error('Search failed');\n                }\n                const data = await response.json();\n                return data.results || [];\n            } catch (error) {\n                console.error('Memory search failed:', error);\n                throw error;\n            }\n        }\n    }[\"useEnhancedChat.useCallback[searchMemory]\"], []);\n    const getMemoryContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[getMemoryContext]\": async (sessionId)=>{\n            try {\n                const response = await fetch(\"/api/zep/memory?sessionId=\".concat(sessionId));\n                if (!response.ok) {\n                    throw new Error('Failed to get memory context');\n                }\n                const data = await response.json();\n                return data.memory;\n            } catch (error) {\n                console.error('Failed to get memory context:', error);\n                throw error;\n            }\n        }\n    }[\"useEnhancedChat.useCallback[getMemoryContext]\"], []);\n    const updateConversationInsights = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[updateConversationInsights]\": async (conversationId)=>{\n            const conversation = state.conversations.find({\n                \"useEnhancedChat.useCallback[updateConversationInsights].conversation\": (c)=>c.id === conversationId\n            }[\"useEnhancedChat.useCallback[updateConversationInsights].conversation\"]);\n            if (!(conversation === null || conversation === void 0 ? void 0 : conversation.zepSessionId)) return;\n            try {\n                const [factsResponse, entitiesResponse] = await Promise.all([\n                    fetch(\"/api/zep/facts?sessionId=\".concat(conversation.zepSessionId)),\n                    fetch(\"/api/zep/entities?sessionId=\".concat(conversation.zepSessionId))\n                ]);\n                const factsData = factsResponse.ok ? await factsResponse.json() : {\n                    facts: []\n                };\n                const entitiesData = entitiesResponse.ok ? await entitiesResponse.json() : {\n                    entities: []\n                };\n                setState({\n                    \"useEnhancedChat.useCallback[updateConversationInsights]\": (prev)=>({\n                            ...prev,\n                            conversations: prev.conversations.map({\n                                \"useEnhancedChat.useCallback[updateConversationInsights]\": (c)=>c.id === conversationId ? {\n                                        ...c,\n                                        facts: factsData.facts || [],\n                                        entities: entitiesData.entities || [],\n                                        updatedAt: Date.now()\n                                    } : c\n                            }[\"useEnhancedChat.useCallback[updateConversationInsights]\"])\n                        })\n                }[\"useEnhancedChat.useCallback[updateConversationInsights]\"]);\n                // Update storage\n                const updatedConversation = {\n                    ...conversation,\n                    facts: factsData.facts || [],\n                    entities: entitiesData.entities || [],\n                    updatedAt: Date.now()\n                };\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n            } catch (error) {\n                console.error('Failed to update conversation insights:', error);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[updateConversationInsights]\"], [\n        state.conversations\n    ]);\n    const reloadConversations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[reloadConversations]\": async ()=>{\n            try {\n                const conversations = await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.loadConversations();\n                console.log('Reloaded conversations:', conversations.map({\n                    \"useEnhancedChat.useCallback[reloadConversations]\": (c)=>({\n                            id: c.id,\n                            title: c.title,\n                            workspaceId: c.workspaceId\n                        })\n                }[\"useEnhancedChat.useCallback[reloadConversations]\"]));\n                console.log('Full conversation objects:', conversations);\n                setState({\n                    \"useEnhancedChat.useCallback[reloadConversations]\": (prev)=>({\n                            ...prev,\n                            conversations\n                        })\n                }[\"useEnhancedChat.useCallback[reloadConversations]\"]);\n            } catch (error) {\n                console.error('Failed to reload conversations:', error);\n            }\n        }\n    }[\"useEnhancedChat.useCallback[reloadConversations]\"], []);\n    const enableZepForConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEnhancedChat.useCallback[enableZepForConversation]\": async function(conversationId) {\n            let userId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'default-user';\n            const conversation = state.conversations.find({\n                \"useEnhancedChat.useCallback[enableZepForConversation].conversation\": (c)=>c.id === conversationId\n            }[\"useEnhancedChat.useCallback[enableZepForConversation].conversation\"]);\n            if (!conversation || conversation.zepSessionId) {\n                return false; // Already has Zep or conversation not found\n            }\n            try {\n                const zepService = _lib_zep_service__WEBPACK_IMPORTED_MODULE_4__.ZepService.getInstance();\n                if (!zepService.isConfigured()) {\n                    console.warn('Zep service not configured');\n                    return false;\n                }\n                // Create Zep session\n                const zepSessionId = await zepService.addSession(conversationId, userId);\n                console.log('Created Zep session for existing conversation:', zepSessionId);\n                // Update conversation with Zep session ID and userId\n                const updatedConversation = {\n                    ...conversation,\n                    userId,\n                    zepSessionId,\n                    facts: [],\n                    entities: [],\n                    updatedAt: Date.now()\n                };\n                setState({\n                    \"useEnhancedChat.useCallback[enableZepForConversation]\": (prev)=>({\n                            ...prev,\n                            conversations: prev.conversations.map({\n                                \"useEnhancedChat.useCallback[enableZepForConversation]\": (c)=>c.id === conversationId ? updatedConversation : c\n                            }[\"useEnhancedChat.useCallback[enableZepForConversation]\"])\n                        })\n                }[\"useEnhancedChat.useCallback[enableZepForConversation]\"]);\n                // Save to storage\n                await _lib_storage__WEBPACK_IMPORTED_MODULE_1__.StorageService.updateConversation(updatedConversation);\n                // If there are existing messages, send them to Zep\n                if (conversation.messages.length > 0) {\n                    try {\n                        await zepService.addMessages(zepSessionId, conversation.messages.map({\n                            \"useEnhancedChat.useCallback[enableZepForConversation]\": (msg)=>({\n                                    role: msg.role,\n                                    content: msg.content\n                                })\n                        }[\"useEnhancedChat.useCallback[enableZepForConversation]\"]));\n                        console.log('Existing messages sent to Zep successfully');\n                    } catch (error) {\n                        console.warn('Failed to send existing messages to Zep:', error);\n                    }\n                }\n                return true;\n            } catch (error) {\n                console.error('Failed to enable Zep for conversation:', error);\n                return false;\n            }\n        }\n    }[\"useEnhancedChat.useCallback[enableZepForConversation]\"], [\n        state.conversations\n    ]);\n    return {\n        ...state,\n        currentConversation: getCurrentConversation(),\n        createNewConversation,\n        selectConversation,\n        deleteConversation,\n        renameConversation,\n        sendMessage,\n        editMessage,\n        resendMessage,\n        deleteMessage,\n        setMode,\n        toggleSidebar,\n        clearError,\n        checkServiceHealth,\n        reloadConversations,\n        // Zep functions\n        searchMemory,\n        getMemoryContext,\n        updateConversationInsights,\n        enableZepForConversation\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useEnhancedChat.ts\n"));

/***/ })

});