'use client';

import { useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Conversation, Message, ChatState } from '@/types/chat';
import { StorageService } from '@/lib/storage';
import { APIService } from '@/lib/api';
import { EnhancedAPIService, EnhancedChatConfig } from '@/lib/enhanced-api';
import { ZepService } from '@/lib/zep-service';
import { useSettings } from '@/contexts/SettingsContext';

export type ChatMode = 'simple' | 'enhanced' | 'orchestrator' | 'refine';

interface EnhancedChatState extends ChatState {
  currentMode: ChatMode;
  progressStatus?: string;
  serviceHealth?: any;
}

export function useEnhancedChat() {
  const { settings } = useSettings();
  const [state, setState] = useState<EnhancedChatState>({
    conversations: [],
    currentConversationId: null,
    isLoading: false,
    error: null,
    sidebarOpen: false,
    currentMode: 'simple',
    progressStatus: undefined,
    serviceHealth: null
  });

  // Initialize storage service, load conversations and check service health on mount
  useEffect(() => {
    const initializeStorage = async () => {
      try {
        // Initialize the storage service
        await StorageService.initialize();
        
        // Load conversations and current conversation ID
        const conversations = await StorageService.loadConversations();
        const currentConversationId = await StorageService.loadCurrentConversationId();
        
        setState(prev => ({
          ...prev,
          conversations,
          currentConversationId: conversations.find(c => c.id === currentConversationId) ? currentConversationId : null
        }));
        
        // Check service health after initialization
        checkServiceHealth();
      } catch (error) {
        console.error('Failed to initialize storage:', error);
        // Fallback to basic localStorage loading
        const conversations = await StorageService.loadConversations();
        const currentConversationId = await StorageService.loadCurrentConversationId();
        
        setState(prev => ({
          ...prev,
          conversations,
          currentConversationId: conversations.find(c => c.id === currentConversationId) ? currentConversationId : null
        }));
        
        // Still check service health
        checkServiceHealth();
      }
    };

    initializeStorage();

    // Cleanup on unmount
    return () => {
      // No cleanup needed for local storage
    };
  }, []);

  // Add periodic save and beforeunload save
  useEffect(() => {
    // Save conversations periodically (every 30 seconds)
    const saveInterval = setInterval(async () => {
      try {
        if (state.conversations.length > 0) {
          console.log('Periodic save: Saving conversations to database');
          await StorageService.saveConversations(state.conversations);
          console.log('Periodic save: Successfully saved conversations');
        }
      } catch (error) {
        console.error('Periodic save failed:', error);
      }
    }, 30000);

    // Save on page unload
    const handleBeforeUnload = async () => {
      try {
        if (state.conversations.length > 0) {
          console.log('Before unload: Saving conversations to database');
          await StorageService.saveConversations(state.conversations);
          console.log('Before unload: Successfully saved conversations');
        }
      } catch (error) {
        console.error('Before unload save failed:', error);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      clearInterval(saveInterval);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [state.conversations]);

  const checkServiceHealth = useCallback(async () => {
    try {
      console.log('useEnhancedChat: Checking service health...');
      const health = await EnhancedAPIService.checkServiceHealth();
      console.log('useEnhancedChat: Service health received:', health);
      setState(prev => ({ ...prev, serviceHealth: health }));
    } catch (error) {
      console.error('useEnhancedChat: Failed to check service health:', error);
      setState(prev => ({ ...prev, serviceHealth: null }));
    }
  }, []);

  const getCurrentConversation = useCallback((): Conversation | null => {
    if (!state.currentConversationId) {
      return null;
    }
    const found = state.conversations.find(c => c.id === state.currentConversationId) || null;
    return found;
  }, [state.conversations, state.currentConversationId]);

  const createNewConversation = useCallback(async (title?: string, userId?: string): Promise<string> => {
    const conversationId = uuidv4();
    let zepSessionId: string | undefined;
    
    // Create Zep session if service is configured and userId is provided
    if (userId) {
      try {
        const zepService = ZepService.getInstance();
        if (zepService.isConfigured()) {
          zepSessionId = await zepService.addSession(conversationId, userId);
          console.log('Created Zep session:', zepSessionId);
        }
      } catch (error) {
        console.warn('Failed to create Zep session:', error);
      }
    }
    
    const newConversation: Conversation = {
      id: conversationId,
      title: title || 'New Chat',
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      userId,
      zepSessionId,
      facts: [],
      entities: []
    };

    setState(prev => ({
      ...prev,
      conversations: [newConversation, ...prev.conversations],
      currentConversationId: newConversation.id,
      error: null
    }));

    await StorageService.updateConversation(newConversation);
    await StorageService.saveCurrentConversationId(newConversation.id);

    return newConversation.id;
  }, []);

  const selectConversation = useCallback(async (id: string) => {
    setState(prev => ({
      ...prev,
      currentConversationId: id,
      error: null
    }));
    await StorageService.saveCurrentConversationId(id);
  }, []);

  const deleteConversation = useCallback(async (id: string) => {
    setState(prev => {
      const newConversations = prev.conversations.filter(c => c.id !== id);
      const newCurrentId = prev.currentConversationId === id 
        ? (newConversations.length > 0 ? newConversations[0].id : null)
        : prev.currentConversationId;

      return {
        ...prev,
        conversations: newConversations,
        currentConversationId: newCurrentId,
        error: null
      };
    });

    await StorageService.deleteConversation(id);
  }, []);

  const renameConversation = useCallback(async (id: string, newTitle: string) => {
    // Update local state immediately for responsive UI
    setState(prev => ({
      ...prev,
      conversations: prev.conversations.map(c => 
        c.id === id 
          ? { ...c, title: newTitle, updatedAt: Date.now() }
          : c
      )
    }));

    try {
      // Use the new dedicated rename method
      await StorageService.renameConversation(id, newTitle);
    } catch (error) {
      console.error('Failed to rename conversation:', error);
      // Revert the local state change on error
      setState(prev => ({
        ...prev,
        conversations: prev.conversations.map(c => 
          c.id === id 
            ? { ...c, title: state.conversations.find(conv => conv.id === id)?.title || 'New Chat' }
            : c
        )
      }));
    }
  }, [state.conversations]);

  const setMode = useCallback((mode: ChatMode) => {
    console.log('🔄 MODE CHANGE: Setting mode to:', mode);
    setState(prev => ({ ...prev, currentMode: mode }));
  }, []);

  const sendMessage = useCallback(async (content: string, config?: Partial<EnhancedChatConfig>, isResend: boolean = false) => {
    console.log('sendMessage called with content:', content, 'isResend:', isResend);
    if (!content.trim()) {
      console.log('Content is empty, returning');
      return;
    }

    // Get current state values directly from state
    let conversationId = state.currentConversationId;
    let currentConversations = state.conversations;
    let currentMode = state.currentMode;

    // Create new conversation if none exists
    if (!conversationId) {
      const newConversation: Conversation = {
        id: uuidv4(),
        title: content.slice(0, 50),
        messages: [],
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      console.log('Creating new conversation:', {
        newConversationId: newConversation.id,
        title: newConversation.title,
        prevConversationsCount: currentConversations.length
      });

      conversationId = newConversation.id;
      currentConversations = [newConversation, ...currentConversations];

      await StorageService.updateConversation(newConversation);
      await StorageService.saveCurrentConversationId(newConversation.id);

      console.log('New conversation created and saved:', {
        conversationId,
        newConversationsCount: currentConversations.length,
        conversationIds: currentConversations.map(c => c.id)
      });

      setState(prev => ({
        ...prev,
        conversations: currentConversations,
        currentConversationId: conversationId,
        error: null
      }));
    }
    
    console.log('🚀 SEND MESSAGE called with mode:', {
      currentMode,
      content: content.substring(0, 50),
      config,
      conversationId
    });

    // For resend operations, get the latest conversation state to ensure we have updated content
    let conversation: Conversation | null;
    if (isResend) {
      conversation = getCurrentConversation();
      console.log('useEnhancedChat: Getting latest conversation for resend:', {
        conversationId,
        found: !!conversation,
        messageCount: conversation?.messages?.length || 0
      });
    } else {
      conversation = currentConversations.find(c => c.id === conversationId) || null;
      console.log('useEnhancedChat: Looking for conversation:', {
        conversationId,
        availableConversations: currentConversations.map(c => c.id),
        found: !!conversation
      });
    }

    if (!conversation) {
      console.error('Conversation not found:', conversationId);
      return;
    }

    let userMessage: Message;
    let messagesToAdd: Message[];

    if (isResend) {
      // For resend, we assume the user message is already updated in the conversation
      // We only need to add a new assistant message
      const assistantMessage: Message = {
        id: uuidv4(),
        role: 'assistant',
        content: '',
        timestamp: Date.now(),
        isStreaming: true
      };

      // Use the content parameter for the user message (this contains the updated content)
      // Get the last user message from conversation for metadata (id, timestamp, etc.)
      const lastUserMessage = conversation.messages.filter(m => m.role === 'user').pop();
      if (!lastUserMessage) {
        console.error('No user message found for resend');
        return;
      }
      // Create userMessage with updated content but preserve other properties
      userMessage = {
        ...lastUserMessage,
        content: content.trim() // Use the updated content passed to sendMessage
      };
      messagesToAdd = [assistantMessage];
    } else {
      // Normal send: create both user and assistant messages
      userMessage = {
        id: uuidv4(),
        role: 'user',
        content: content.trim(),
        timestamp: Date.now()
      };

      const assistantMessage: Message = {
        id: uuidv4(),
        role: 'assistant',
        content: '',
        timestamp: Date.now(),
        isStreaming: true
      };
      
      messagesToAdd = [userMessage, assistantMessage];
    }

    // Add messages to conversation
    const updatedConversation = {
      ...conversation,
      messages: [...conversation.messages, ...messagesToAdd],
      updatedAt: Date.now(),
      title: conversation.messages.length === 0 ? content.slice(0, 50) : conversation.title
    };

    // Get the assistant message for later reference
    const assistantMessage = messagesToAdd.find(m => m.role === 'assistant');
    if (!assistantMessage) {
      console.error('No assistant message found in messagesToAdd');
      return;
    }

    // Update state and save to storage immediately
    setState(prev => {
      console.log('setState: Adding messages to conversation', {
        conversationId,
        prevConversationsCount: prev.conversations.length,
        updatedConversationMessagesCount: updatedConversation.messages.length,
        userMessage: { id: userMessage.id, role: userMessage.role, content: userMessage.content?.substring(0, 50) },
        assistantMessage: { id: assistantMessage.id, role: assistantMessage.role, content: assistantMessage.content },
        isResend
      });

      const newState = {
        ...prev,
        conversations: prev.conversations.map(c =>
          c.id === conversationId
            ? updatedConversation
            : c
        ),
        currentConversationId: conversationId, // Ensure we maintain the correct conversation ID
        isLoading: true,
        error: null
      };

      console.log('setState: New state after adding messages', {
        conversationsCount: newState.conversations.length,
        currentConversationId: newState.currentConversationId,
        currentConversation: newState.conversations.find(c => c.id === conversationId)?.messages.map(m => ({
          id: m.id,
          role: m.role,
          contentLength: m.content?.length
        }))
      });

      return newState;
    });

    // Save updated conversation to storage with error handling
    try {
      await StorageService.updateConversation(updatedConversation);
    } catch (storageError) {
      console.warn('Failed to save conversation to storage during sendMessage, but continuing:', storageError);
      // Don't throw the error - the message was added to state successfully, so continue
    }

    // Messages added to state

    try {
      const messages = [...conversation.messages, userMessage].map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content
      }));

      let fullResponse = '';
      console.log(`🚀 SEND MESSAGE called with mode: ${currentMode}`);

      // Use appropriate API based on mode
      if (currentMode === 'simple') {
        // Use original API service for simple mode
        const modelToUse = settings.preferredModels.simple;
        const maxTokens = settings.responseSettings.streaming.simple;
        await APIService.sendMessage(
          messages,
          // onChunk
          (chunk: string) => {
            fullResponse += chunk;
            setState(prev => {
              const newState = {
                ...prev,
                conversations: prev.conversations.map(c =>
                  c.id === conversationId
                    ? {
                        ...c,
                        messages: c.messages.map(m =>
                          m.id === assistantMessage.id
                            ? { ...m, content: fullResponse }
                            : m
                        )
                      }
                    : c
                )
              };
              return newState;
            });
          },
          // onComplete
          async () => {
            console.log('Simple mode message completed');
            // Check if this is the first user message (conversation had no messages before)
            const isFirstMessage = conversation.messages.length === 0;
            let finalConversationForZep: Conversation | undefined;
            
            setState(prev => {
              const updatedConversations = prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.map(m =>
                        m.id === assistantMessage.id
                          ? { ...m, content: fullResponse, isStreaming: false }
                          : m
                      ),
                      updatedAt: Date.now()
                    }
                  : c
              );

              finalConversationForZep = updatedConversations.find(c => c.id === conversationId);

              return {
                ...prev,
                conversations: updatedConversations,
                isLoading: false
              };
            });

            // Save to storage with error handling
            if (finalConversationForZep) {
              try {
                console.log(`Saving conversation after ${currentMode} mode completion:`, {
                  conversationId: finalConversationForZep.id,
                  messageCount: finalConversationForZep.messages.length,
                  lastMessageContent: finalConversationForZep.messages[finalConversationForZep.messages.length - 1]?.content?.substring(0, 100)
                });
                await StorageService.updateConversation(finalConversationForZep);
                console.log(`Successfully saved conversation after ${currentMode} mode completion`);
              } catch (error) {
                console.error(`Failed to save conversation after ${currentMode} mode completion:`, error);
                // Try to save again after a short delay
                setTimeout(async () => {
                  try {
                    console.log(`Retrying conversation save after ${currentMode} mode completion`);
                    await StorageService.updateConversation(finalConversationForZep);
                    console.log(`Successfully saved conversation on retry after ${currentMode} mode completion`);
                  } catch (retryError) {
                    console.error(`Failed to save conversation on retry after ${currentMode} mode completion:`, retryError);
                  }
                }, 1000);
              }
            }

            // Generate title for first message (only for queries with more than 6 words)
            if (isFirstMessage && finalConversationForZep) {
              const wordCount = content.trim().split(/\s+/).length;
              if (wordCount > 6) {
                try {
                  const response = await fetch('/api/generate-title', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      userMessage: content.trim(),
                      model: settings.preferredModels?.titleGeneration
                    }),
                  });

                  if (response.ok) {
                    const data = await response.json();
                    const generatedTitle = data.title;

                    // Update conversation with generated title
                    setState(prev => {
                      const updatedConversations = prev.conversations.map(c =>
                        c.id === conversationId
                          ? { ...c, title: generatedTitle, updatedAt: Date.now() }
                          : c
                      );

                      // Save updated conversation to storage
                      const updatedConversation = updatedConversations.find(c => c.id === conversationId);
                      if (updatedConversation) {
                        StorageService.updateConversation(updatedConversation);
                      }

                      return {
                        ...prev,
                        conversations: updatedConversations
                      };
                    });
                  }
                } catch (error) {
                  console.error('Failed to generate title:', error);
                  // Title generation failure is not critical, continue without it
                }
              } else {
                console.log(`Skipping title generation: query has ${wordCount} words (need >6)`);
              }
            }

            // Send messages to Zep if configured
            if (finalConversationForZep?.zepSessionId) {
              try {
                const zepService = ZepService.getInstance();
                if (zepService.isConfigured()) {
                  await zepService.addMessages(finalConversationForZep.zepSessionId, [
                     { role: 'user', content: userMessage.content },
                     { role: 'assistant', content: fullResponse }
                   ]);
                   console.log('Messages sent to Zep successfully');
                 }
               } catch (error) {
                 console.warn('Failed to send messages to Zep:', error);
               }
             }
          },
          // onError
          (error: string) => {
            console.error('Simple mode API error:', error);
            setState(prev => ({
              ...prev,
              conversations: prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.filter(m => m.id !== assistantMessage.id)
                    }
                  : c
              ),
              isLoading: false,
              error,
              progressStatus: undefined
            }));
          },
          // model
          modelToUse,
          // maxTokens
          maxTokens
        );
      } else {
        // Use enhanced API service for enhanced and orchestrator modes
        const enhancedConfig: EnhancedChatConfig = {
          mode: currentMode === 'orchestrator' ? 'orchestrator' : 'single',
          maxIterations: config?.maxIterations || 3,
          parallelAgents: config?.parallelAgents || 3,
          timeout: config?.timeout || 300000,
          // Force tools to be enabled when in enhanced mode (user clicked Tools button)
          toolsEnabled: currentMode === 'enhanced' ? true : (config?.toolsEnabled !== false)
        };

        console.log(`🔧 Enhanced mode config:`, {
          mode: enhancedConfig.mode,
          toolsEnabled: enhancedConfig.toolsEnabled,
          currentMode,
          toolsForced: currentMode === 'enhanced'
        });

        await EnhancedAPIService.sendEnhancedMessage(
          messages,
          enhancedConfig,
          // onChunk
          (chunk: string) => {
            fullResponse += chunk;
            setState(prev => {
              const updatedConversations = prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.map(m =>
                        m.id === assistantMessage.id
                          ? { ...m, content: fullResponse }
                          : m
                      )
                    }
                  : c
              );

              return {
                ...prev,
                conversations: updatedConversations,
                currentConversationId: conversationId // Maintain conversation ID during streaming
              };
            });
          },
          // onComplete
          async () => {
            console.log(`${currentMode} mode message completed`);
            // Check if this is the first user message (conversation had no messages before)
            const isFirstMessage = conversation.messages.length === 0;
            let finalConversationForZep: Conversation | undefined;
            
            setState(prev => {
              const updatedConversations = prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.map(m =>
                        m.id === assistantMessage.id
                          ? { ...m, content: fullResponse, isStreaming: false }
                          : m
                      ),
                      updatedAt: Date.now()
                    }
                  : c
              );

              finalConversationForZep = updatedConversations.find(c => c.id === conversationId);

              return {
                ...prev,
                conversations: updatedConversations,
                isLoading: false
              };
            });

            // Save to storage with error handling
            if (finalConversationForZep) {
              try {
                console.log(`Saving conversation after ${currentMode} mode completion:`, {
                  conversationId: finalConversationForZep.id,
                  messageCount: finalConversationForZep.messages.length,
                  lastMessageContent: finalConversationForZep.messages[finalConversationForZep.messages.length - 1]?.content?.substring(0, 100)
                });
                await StorageService.updateConversation(finalConversationForZep);
                console.log(`Successfully saved conversation after ${currentMode} mode completion`);
              } catch (error) {
                console.error(`Failed to save conversation after ${currentMode} mode completion:`, error);
                // Try to save again after a short delay
                setTimeout(async () => {
                  try {
                    console.log(`Retrying conversation save after ${currentMode} mode completion`);
                    await StorageService.updateConversation(finalConversationForZep);
                    console.log(`Successfully saved conversation on retry after ${currentMode} mode completion`);
                  } catch (retryError) {
                    console.error(`Failed to save conversation on retry after ${currentMode} mode completion:`, retryError);
                  }
                }, 1000);
              }
            }

            // Generate title for first message (only for queries with more than 6 words)
            if (isFirstMessage && finalConversationForZep) {
              const wordCount = content.trim().split(/\s+/).length;
              if (wordCount > 6) {
                try {
                  const response = await fetch('/api/generate-title', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                      userMessage: content.trim(),
                      model: settings.preferredModels?.titleGeneration
                    }),
                  });

                  if (response.ok) {
                    const data = await response.json();
                    const generatedTitle = data.title;

                    // Update conversation with generated title
                    setState(prev => {
                      const updatedConversations = prev.conversations.map(c =>
                        c.id === conversationId
                          ? { ...c, title: generatedTitle, updatedAt: Date.now() }
                          : c
                      );

                      // Save updated conversation to storage
                      const updatedConversation = updatedConversations.find(c => c.id === conversationId);
                      if (updatedConversation) {
                        StorageService.updateConversation(updatedConversation);
                      }

                      return {
                        ...prev,
                        conversations: updatedConversations
                      };
                    });
                  }
                } catch (error) {
                  console.error('Failed to generate title:', error);
                  // Title generation failure is not critical, continue without it
                }
              } else {
                console.log(`Skipping title generation: query has ${wordCount} words (need >6)`);
              }
            }

            // Send messages to Zep if configured
            if (finalConversationForZep?.zepSessionId) {
              try {
                const zepService = ZepService.getInstance();
                if (zepService.isConfigured()) {
                  await zepService.addMessages(finalConversationForZep.zepSessionId, [
                    { role: 'user', content: userMessage.content },
                    { role: 'assistant', content: fullResponse }
                  ]);
                  console.log('Messages sent to Zep successfully');
                }
              } catch (error) {
                console.warn('Failed to send messages to Zep:', error);
              }
            }
          },
          // onError
          (error: string) => {
            console.error(`${currentMode} mode API error:`, error);
            setState(prev => {
              const updatedConversations = prev.conversations.map(c =>
                c.id === conversationId
                  ? {
                      ...c,
                      messages: c.messages.filter(m => m.id !== assistantMessage.id)
                    }
                  : c
              );

              return {
                ...prev,
                conversations: updatedConversations,
                currentConversationId: conversationId, // Maintain conversation ID
                isLoading: false,
                error,
                progressStatus: undefined
              };
            });
          },
          // onProgress (for orchestrator mode)
          (status: string) => {
            console.log('Progress update:', status);
            setState(prev => ({ ...prev, progressStatus: status }));
          }
        );
      }
    } catch (error) {
      console.error('Send message error (catch block):', error);
      console.error('catch: Removing assistant message and updating state');
      setState(prev => {
        const updatedConversations = prev.conversations.map(c =>
          c.id === conversationId
            ? {
                ...c,
                messages: c.messages.filter(m => m.id !== assistantMessage.id)
              }
            : c
        );

        console.error('catch: Updated conversations after error:', updatedConversations.map(c => ({
          id: c.id,
          messageCount: c.messages.length,
          messages: c.messages.map(m => ({ id: m.id, role: m.role, contentLength: m.content?.length || 0 }))
        })));

        return {
          ...prev,
          conversations: updatedConversations,
          currentConversationId: conversationId, // Maintain conversation ID
          isLoading: false,
          error: error instanceof Error ? error.message : 'An error occurred',
          progressStatus: undefined
        };
      });
    }
  }, [state.currentConversationId, state.conversations, state.currentMode, settings]);

  const toggleSidebar = useCallback(() => {
    setState(prev => ({ ...prev, sidebarOpen: !prev.sidebarOpen }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const editMessage = useCallback(async (messageId: string, newContent: string) => {
    let updatedConversation: Conversation | undefined;
    
    setState(prev => {
      const updatedConversations = prev.conversations.map(conversation => {
        if (conversation.id === prev.currentConversationId) {
          const updatedMessages = conversation.messages.map(message => 
            message.id === messageId 
              ? { ...message, content: newContent }
              : message
          );
          updatedConversation = { ...conversation, messages: updatedMessages, updatedAt: Date.now() };
          return updatedConversation;
        }
        return conversation;
      });
      
      return {
        ...prev,
        conversations: updatedConversations
      };
    });
    
    // Save to storage
    if (updatedConversation) {
      await StorageService.updateConversation(updatedConversation);
    }
  }, []);

  const resendMessage = useCallback(async (messageId: string, updatedContent?: string) => {
    console.log('resendMessage called with:', { messageId, updatedContent });
    const currentConversation = getCurrentConversation();
    if (!currentConversation) {
      console.log('No current conversation found');
      return;
    }

    const messageIndex = currentConversation.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) {
      console.log('Message not found in conversation');
      return;
    }

    const userMessage = currentConversation.messages[messageIndex];
    if (userMessage.role !== 'user') {
      console.log('Message is not from user');
      return;
    }

    // Use the updated content if provided, otherwise use the current message content
    const contentToSend = updatedContent || userMessage.content;
    console.log('Content to send:', contentToSend);
    console.log('Original message content:', userMessage.content);

    // Remove all messages after the edited user message
    const messagesToKeep = currentConversation.messages.slice(0, messageIndex + 1);
    
    // If we have updated content, update the message in the kept messages
    if (updatedContent) {
      messagesToKeep[messageIndex] = {
        ...userMessage,
        content: updatedContent,
        timestamp: Date.now() // Update timestamp to ensure object reference changes
      };
    }
    
    const updatedConversation = {
      ...currentConversation,
      messages: messagesToKeep,
      updatedAt: Date.now()
    };
    
    // STEP 1: Update the message content in the UI first
    console.log('STEP 1: Updating message content in UI...');
    setState(prev => {
      const updatedConversations = prev.conversations.map(c =>
        c.id === currentConversation.id ? updatedConversation : c
      );

      const newState = {
        ...prev,
        conversations: updatedConversations
      };

      console.log('STEP 1 COMPLETE: Message content updated in UI:', {
        messageId,
        updatedContent,
        messageInState: updatedConversation.messages.find(m => m.id === messageId)?.content?.substring(0, 100),
        conversationId: currentConversation.id,
        totalMessages: updatedConversation.messages.length
      });

      return newState;
    });

    // Save to storage with error handling
    try {
      await StorageService.updateConversation(updatedConversation);
    } catch (storageError) {
      console.warn('Failed to save conversation to storage, but continuing with resend:', storageError);
      // Don't throw the error - the UI update was successful, so continue with the resend
    }

    // STEP 2: Wait for UI to update, then send the new query
    console.log('STEP 2: Waiting for UI update, then sending new query...');
    await new Promise(resolve => setTimeout(resolve, 1000)); // Longer delay to ensure UI updates

    // Resend the message with the correct content
    console.log('STEP 2: Sending new query with content:', contentToSend);
    await sendMessage(contentToSend, undefined, true);
  }, [sendMessage, getCurrentConversation]);

  const deleteMessage = useCallback(async (messageId: string) => {
    console.log('deleteMessage called with messageId:', messageId);
    const currentConversation = getCurrentConversation();
    if (!currentConversation) {
      console.log('No current conversation found');
      return;
    }

    const messageIndex = currentConversation.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) {
      console.log('Message not found in conversation');
      return;
    }

    try {
      console.log('Calling StorageService.deleteMessage');
      // Delete the message from the database
      await StorageService.deleteMessage(currentConversation.id, messageId);
      console.log('Message deleted from storage successfully');
      
      // Update local state after successful deletion
      const updatedMessages = currentConversation.messages.filter(m => m.id !== messageId);
      
      const updatedConversation = {
        ...currentConversation,
        messages: updatedMessages,
        updatedAt: Date.now()
      };
      
      setState(prev => {
        const updatedConversations = prev.conversations.map(c => 
          c.id === currentConversation.id ? updatedConversation : c
        );
        
        return {
          ...prev,
          conversations: updatedConversations
        };
      });
    } catch (error) {
      console.error('Failed to delete message:', error);
      // Optionally show an error message to the user
    }
  }, [getCurrentConversation]);

  // Zep helper functions
  const searchMemory = useCallback(async (userId: string, query: string, limit?: number) => {
    try {
      const response = await fetch('/api/zep/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, query, limit: limit || 10 })
      });
      
      if (!response.ok) {
        throw new Error('Search failed');
      }
      
      const data = await response.json();
      return data.results || [];
    } catch (error) {
      console.error('Memory search failed:', error);
      throw error;
    }
  }, []);

  const getMemoryContext = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/zep/memory?sessionId=${sessionId}`);
      
      if (!response.ok) {
        throw new Error('Failed to get memory context');
      }
      
      const data = await response.json();
      return data.memory;
    } catch (error) {
      console.error('Failed to get memory context:', error);
      throw error;
    }
  }, []);

  const updateConversationInsights = useCallback(async (conversationId: string) => {
    const conversation = state.conversations.find(c => c.id === conversationId);
    if (!conversation?.zepSessionId) return;

    try {
      const [factsResponse, entitiesResponse] = await Promise.all([
        fetch(`/api/zep/facts?sessionId=${conversation.zepSessionId}`),
        fetch(`/api/zep/entities?sessionId=${conversation.zepSessionId}`)
      ]);
      
      const factsData = factsResponse.ok ? await factsResponse.json() : { facts: [] };
      const entitiesData = entitiesResponse.ok ? await entitiesResponse.json() : { entities: [] };
      
      setState(prev => ({
        ...prev,
        conversations: prev.conversations.map(c =>
          c.id === conversationId
            ? {
                ...c,
                facts: factsData.facts || [],
                entities: entitiesData.entities || [],
                updatedAt: Date.now()
              }
            : c
        )
      }));
      
      // Update storage
      const updatedConversation = {
        ...conversation,
        facts: factsData.facts || [],
        entities: entitiesData.entities || [],
        updatedAt: Date.now()
      };
      await StorageService.updateConversation(updatedConversation);
    } catch (error) {
      console.error('Failed to update conversation insights:', error);
    }
  }, [state.conversations]);

  const reloadConversations = useCallback(async () => {
    try {
      const conversations = await StorageService.loadConversations();
      console.log('Reloaded conversations:', conversations.map(c => ({ id: c.id, title: c.title, workspaceId: c.workspaceId })));
      console.log('Full conversation objects:', conversations);
      setState(prev => ({
        ...prev,
        conversations
      }));
    } catch (error) {
      console.error('Failed to reload conversations:', error);
    }
  }, []);

  const enableZepForConversation = useCallback(async (conversationId: string, userId: string = 'default-user'): Promise<boolean> => {
    const conversation = state.conversations.find(c => c.id === conversationId);
    if (!conversation || conversation.zepSessionId) {
      return false; // Already has Zep or conversation not found
    }

    try {
      const zepService = ZepService.getInstance();
      if (!zepService.isConfigured()) {
        console.warn('Zep service not configured');
        return false;
      }

      // Create Zep session
      const zepSessionId = await zepService.addSession(conversationId, userId);
      console.log('Created Zep session for existing conversation:', zepSessionId);

      // Update conversation with Zep session ID and userId
      const updatedConversation = {
        ...conversation,
        userId,
        zepSessionId,
        facts: [],
        entities: [],
        updatedAt: Date.now()
      };

      setState(prev => ({
        ...prev,
        conversations: prev.conversations.map(c =>
          c.id === conversationId ? updatedConversation : c
        )
      }));

      // Save to storage
      await StorageService.updateConversation(updatedConversation);

      // If there are existing messages, send them to Zep
      if (conversation.messages.length > 0) {
        try {
          await zepService.addMessages(zepSessionId, conversation.messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })));
          console.log('Existing messages sent to Zep successfully');
        } catch (error) {
          console.warn('Failed to send existing messages to Zep:', error);
        }
      }

      return true;
    } catch (error) {
      console.error('Failed to enable Zep for conversation:', error);
      return false;
    }
  }, [state.conversations]);

  return {
    ...state,
    currentConversation: getCurrentConversation(),
    createNewConversation,
    selectConversation,
    deleteConversation,
    renameConversation,
    sendMessage,
    editMessage,
    resendMessage,
    deleteMessage,
    setMode,
    toggleSidebar,
    clearError,
    checkServiceHealth,
    reloadConversations,
    // Zep functions
    searchMemory,
    getMemoryContext,
    updateConversationInsights,
    enableZepForConversation
  };
}
