import { APIMessage, APIRequest, StreamChunk } from '@/types/chat';

const API_URL = 'https://llm.chutes.ai/v1/chat/completions';
const DEFAULT_MODEL = 'Qwen/Qwen3-235B-A22B-Instruct-2507';
const MAX_RETRIES = 3;
const RETRY_DELAY = 5000; // 5 seconds

export class APIService {
  private static async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private static async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = MAX_RETRIES,
    baseDelay: number = RETRY_DELAY
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // Check if it's a 503 error (service overload)
        if (error instanceof Error && error.message.includes('503')) {
          if (attempt < maxRetries) {
            const delay = baseDelay * Math.pow(2, attempt); // Exponential backoff
            console.log(`Attempt ${attempt + 1} failed with 503 error. Retrying in ${delay}ms...`);
            await this.sleep(delay);
            continue;
          }
        }
        
        // For non-503 errors or final attempt, throw immediately
        throw error;
      }
    }
    
    throw lastError!;
  }

  private static getApiKey(): string {
    // In a real app, this should be from environment variables
    // For now, we'll expect it to be set in the browser's environment
    return process.env.NEXT_PUBLIC_CHUTES_API_TOKEN || '';
  }

  static async sendMessage(
    messages: APIMessage[],
    onChunk?: (content: string) => void,
    onComplete?: () => void,
    onError?: (error: string) => void,
    model?: string,
    maxTokens?: number
  ): Promise<string> {
    const apiKey = this.getApiKey();

    if (!apiKey) {
      const error = 'API key not found. Please set NEXT_PUBLIC_CHUTES_API_TOKEN environment variable.';
      console.error(error);
      onError?.(error);
      throw new Error(error);
    }

    const request: APIRequest = {
      model: model || DEFAULT_MODEL,
      messages,
      stream: true,
      max_tokens: 1024,
      temperature: 0.7
    };

    console.log('Sending API request:', { url: API_URL, model: model || DEFAULT_MODEL, messageCount: messages.length });

    try {
      const response = await this.retryWithBackoff(async () => {
        const response = await fetch(API_URL, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(request)
        });
        
        if (!response.ok) {
          const errorText = await response.text();
          const error = `API request failed: ${response.status} ${response.statusText} - ${errorText}`;
          throw new Error(error);
        }
        
        return response;
      });

      console.log('API response status:', response.status, response.statusText);

      if (!response.body) {
        const error = 'No response body received';
        console.error(error);
        onError?.(error);
        throw new Error(error);
      }

      console.log('Starting stream processing...');
      return await this.processStreamResponse(response.body, onChunk, onComplete, onError);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('API request error:', error);
      onError?.(errorMessage);
      throw error;
    }
  }

  private static async processStreamResponse(
    body: ReadableStream<Uint8Array>,
    onChunk?: (content: string) => void,
    onComplete?: () => void,
    onError?: (error: string) => void
  ): Promise<string> {
    const reader = body.getReader();
    const decoder = new TextDecoder();
    let fullContent = '';
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          console.log('Stream completed, full content:', fullContent);
          onComplete?.();
          break;
        }

        // Decode the chunk and add to buffer
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // Process complete lines (split by double newlines for SSE format)
        const parts = buffer.split('\n\n');
        buffer = parts.pop() || ''; // Keep incomplete part in buffer

        for (const part of parts) {
          const lines = part.split('\n');

          for (const line of lines) {
            const trimmedLine = line.trim();

            if (trimmedLine.startsWith('data: ')) {
              const data = trimmedLine.slice(6).trim();

              if (data === '[DONE]') {
                console.log('Received [DONE], completing stream');
                onComplete?.();
                return fullContent;
              }

              if (data === '' || data === '{}') {
                continue; // Skip empty data lines
              }

              try {
                const parsed: StreamChunk = JSON.parse(data);
                const content = parsed.choices[0]?.delta?.content;

                if (content) {
                  fullContent += content;
                  onChunk?.(content);
                }

                // Check for finish_reason
                if (parsed.choices[0]?.finish_reason === 'stop') {
                  console.log('Received stop signal');
                  onComplete?.();
                  return fullContent;
                }
              } catch (parseError) {
                // Skip invalid JSON chunks
                console.warn('Failed to parse chunk:', data, parseError);
              }
            }
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Stream processing error';
      console.error('API Stream processing error:', error);
      console.error('API: Calling onError callback with:', errorMessage);
      onError?.(errorMessage);
      throw error;
    } finally {
      reader.releaseLock();
    }

    return fullContent;
  }

  static async sendNonStreamingMessage(messages: APIMessage[], model?: string): Promise<string> {
    const apiKey = this.getApiKey();
    
    if (!apiKey) {
      throw new Error('API key not found. Please set NEXT_PUBLIC_CHUTES_API_TOKEN environment variable.');
    }

    const request: Omit<APIRequest, 'stream'> & { stream: false } = {
      model: model || DEFAULT_MODEL,
      messages,
      stream: false,
      max_tokens: 4000,
      temperature: 0.7
    };

    console.log('Sending non-streaming API request:', {
      url: API_URL,
      model: model || DEFAULT_MODEL,
      messageCount: messages.length
    });

    try {
      const response = await this.retryWithBackoff(async () => {
        const response = await fetch(API_URL, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(request)
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
        }

        return response;
      });

      const data = await response.json();
      return data.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('Non-streaming API request failed:', error);
      throw error;
    }
  }

  static async refineText(text: string, model?: string): Promise<string> {
    const refinePrompt = `Please refine and improve the following text to make it clearer, more precise, and better suited for AI interaction. Keep the original intent but enhance clarity, grammar, and structure. Return ONLY the refined text without any additional commentary or explanation:

"${text}"

Refined version:`;
    
    const messages: APIMessage[] = [
      {
        role: 'user',
        content: refinePrompt
      }
    ];

    const refinedText = await APIService.sendNonStreamingMessage(messages, model);
    // Remove leading/trailing quotes and newlines
    return refinedText.replace(/^[\n\r"]+|[\n\r"]+$/g, '').trim();
  }
}
