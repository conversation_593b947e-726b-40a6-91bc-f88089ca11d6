export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  isStreaming?: boolean;
}

export interface Workspace {
  id: string;
  name: string;
  description?: string;
  color: string;
  position: number;
  createdAt: number;
  updatedAt: number;
  userId?: string;
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: number;
  updatedAt: number;
  workspaceId?: string; // Reference to workspace
  userId?: string; // Add user identification
  zepSessionId?: string; // Link to Zep session
  facts?: ZepFact[]; // Store extracted facts
  entities?: ZepEntity[]; // Store extracted entities
}

export interface ChatState {
  conversations: Conversation[];
  workspaces: Workspace[];
  currentConversationId: string | null;
  currentWorkspaceId: string | null;
  isLoading: boolean;
  error: string | null;
  sidebarOpen: boolean;
}

export interface APIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface APIRequest {
  model: string;
  messages: APIMessage[];
  stream: boolean;
  max_tokens: number;
  temperature: number;
}

export interface APIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface StreamChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    delta: {
      role?: string;
      content?: string;
    };
    finish_reason: string | null;
  }[];
}

// Zep-related interfaces
export interface ZepFact {
  id: string;
  fact: string;
  rating: number;
  created_at: string;
}

export interface ZepEntity {
  name: string;
  type: string;
  description?: string;
}

export interface ZepMemoryContext {
  context: string;
  facts: ZepFact[];
  entities: ZepEntity[];
}
