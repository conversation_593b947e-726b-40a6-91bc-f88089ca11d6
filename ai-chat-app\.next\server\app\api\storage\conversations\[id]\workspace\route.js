/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/storage/conversations/[id]/workspace/route";
exports.ids = ["app/api/storage/conversations/[id]/workspace/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute&page=%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute&page=%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_USER_Dev_AI_Chat_chat11_ai_chat_app_src_app_api_storage_conversations_id_workspace_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/storage/conversations/[id]/workspace/route.ts */ \"(rsc)/./src/app/api/storage/conversations/[id]/workspace/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_USER_Dev_AI_Chat_chat11_ai_chat_app_src_app_api_storage_conversations_id_workspace_route_ts__WEBPACK_IMPORTED_MODULE_16__]);\nC_Users_USER_Dev_AI_Chat_chat11_ai_chat_app_src_app_api_storage_conversations_id_workspace_route_ts__WEBPACK_IMPORTED_MODULE_16__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/storage/conversations/[id]/workspace/route\",\n        pathname: \"/api/storage/conversations/[id]/workspace\",\n        filename: \"route\",\n        bundlePath: \"app/api/storage/conversations/[id]/workspace/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Dev\\\\AI\\\\Chat\\\\chat11\\\\ai-chat-app\\\\src\\\\app\\\\api\\\\storage\\\\conversations\\\\[id]\\\\workspace\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_USER_Dev_AI_Chat_chat11_ai_chat_app_src_app_api_storage_conversations_id_workspace_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/storage/conversations/[id]/workspace/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute&page=%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/storage/conversations/[id]/workspace/route.ts":
/*!*******************************************************************!*\
  !*** ./src/app/api/storage/conversations/[id]/workspace/route.ts ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_database__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_database__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// PUT /api/storage/conversations/[id]/workspace - Assign conversation to workspace\nasync function PUT(request, { params }) {\n    try {\n        const { workspaceId } = await request.json();\n        const { id: conversationId } = await params;\n        const deviceId = request.nextUrl.searchParams.get('deviceId');\n        if (!deviceId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Device ID is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log('API: Assigning conversation', conversationId, 'to workspace', workspaceId, 'for device', deviceId);\n        const result = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.DatabaseService.assignConversationToWorkspace(deviceId, conversationId, workspaceId);\n        console.log('API: Assignment result:', result);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error('Failed to assign conversation to workspace:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to assign conversation to workspace'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/storage/conversations/[id]/workspace/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseService: () => (/* binding */ DatabaseService)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// Database configuration\nconst dbConfig = {\n    host: process.env.DB_HOST || 'localhost',\n    port: parseInt(process.env.DB_PORT || '5432'),\n    database: process.env.DB_NAME || 'ai_chat_app',\n    user: process.env.DB_USER || 'postgres',\n    password: process.env.DB_PASSWORD || 'nexus123',\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 10000,\n    // Disable SSL for local development\n    ssl: false\n};\n// Debug logging\nconsole.log('Database config:', {\n    host: dbConfig.host,\n    port: dbConfig.port,\n    database: dbConfig.database,\n    user: dbConfig.user,\n    password: dbConfig.password === '' ? 'empty_string' : dbConfig.password ? '***' : 'undefined'\n});\n// Create connection pool\nlet pool = null;\nfunction getPool() {\n    if (!pool) {\n        pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\n        // Handle pool errors\n        pool.on('error', (err)=>{\n            console.error('Unexpected error on idle client', err);\n        });\n    }\n    return pool;\n}\n// Database utility functions\nclass DatabaseService {\n    static async getClient() {\n        const pool = getPool();\n        return await pool.connect();\n    }\n    // Initialize database (create tables if they don't exist)\n    static async initialize() {\n        const client = await this.getClient();\n        try {\n            // Read and execute schema\n            const fs = (__webpack_require__(/*! fs */ \"fs\").promises);\n            const path = __webpack_require__(/*! path */ \"path\");\n            const schemaPath = path.join(process.cwd(), 'database', 'schema.sql');\n            try {\n                const schema = await fs.readFile(schemaPath, 'utf-8');\n                await client.query(schema);\n                console.log('Database schema initialized successfully');\n            } catch (error) {\n                console.warn('Could not read schema file, database may need manual setup:', error);\n            }\n        } finally{\n            client.release();\n        }\n    }\n    // User management\n    static async findOrCreateUser(deviceId) {\n        const client = await this.getClient();\n        try {\n            // Try to find existing user\n            const findResult = await client.query('SELECT id FROM users WHERE device_id = $1', [\n                deviceId\n            ]);\n            if (findResult.rows.length > 0) {\n                return findResult.rows[0].id;\n            }\n            // Create new user\n            const createResult = await client.query('INSERT INTO users (device_id) VALUES ($1) RETURNING id', [\n                deviceId\n            ]);\n            return createResult.rows[0].id;\n        } finally{\n            client.release();\n        }\n    }\n    // Conversation management\n    static async saveConversations(deviceId, conversations) {\n        const client = await this.getClient();\n        try {\n            await client.query('BEGIN');\n            const userId = await this.findOrCreateUser(deviceId);\n            for (const conversation of conversations){\n                // Upsert conversation\n                await client.query(`\n          INSERT INTO conversations (id, user_id, workspace_id, title, created_at, updated_at)\n          VALUES ($1, $2, $3, $4, $5, $6)\n          ON CONFLICT (id) DO UPDATE SET\n            workspace_id = EXCLUDED.workspace_id,\n            title = EXCLUDED.title,\n            updated_at = EXCLUDED.updated_at\n        `, [\n                    conversation.id,\n                    userId,\n                    conversation.workspaceId || null,\n                    conversation.title,\n                    new Date(conversation.createdAt),\n                    new Date(conversation.updatedAt)\n                ]);\n                // Delete existing messages for this conversation\n                await client.query('DELETE FROM messages WHERE conversation_id = $1', [\n                    conversation.id\n                ]);\n                // Insert messages\n                for (const message of conversation.messages){\n                    await client.query(`\n            INSERT INTO messages (id, conversation_id, role, content, timestamp, is_streaming)\n            VALUES ($1, $2, $3, $4, $5, $6)\n          `, [\n                        message.id,\n                        conversation.id,\n                        message.role,\n                        message.content,\n                        new Date(message.timestamp),\n                        message.isStreaming || false\n                    ]);\n                }\n            }\n            await client.query('COMMIT');\n        } catch (error) {\n            await client.query('ROLLBACK');\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    static async deleteMessage(deviceId, conversationId, messageId) {\n        const client = await this.getClient();\n        try {\n            await client.query('BEGIN');\n            const userId = await this.findOrCreateUser(deviceId);\n            // Verify the conversation belongs to this user\n            const convResult = await client.query('SELECT id FROM conversations WHERE id = $1 AND user_id = $2', [\n                conversationId,\n                userId\n            ]);\n            if (convResult.rows.length === 0) {\n                throw new Error('Conversation not found or access denied');\n            }\n            // Delete the specific message\n            const deleteResult = await client.query('DELETE FROM messages WHERE id = $1 AND conversation_id = $2', [\n                messageId,\n                conversationId\n            ]);\n            if (deleteResult.rowCount === 0) {\n                throw new Error('Message not found');\n            }\n            // Update conversation's updated_at timestamp\n            await client.query('UPDATE conversations SET updated_at = CURRENT_TIMESTAMP WHERE id = $1', [\n                conversationId\n            ]);\n            await client.query('COMMIT');\n        } catch (error) {\n            await client.query('ROLLBACK');\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    static async loadConversations(deviceId) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            // Load conversations\n            const conversationsResult = await client.query(`\n        SELECT id, title, workspace_id, created_at, updated_at\n        FROM conversations\n        WHERE user_id = $1\n        ORDER BY updated_at DESC\n      `, [\n                userId\n            ]);\n            const conversations = [];\n            for (const convRow of conversationsResult.rows){\n                // Load messages for this conversation\n                const messagesResult = await client.query(`\n          SELECT id, role, content, timestamp, is_streaming\n          FROM messages\n          WHERE conversation_id = $1\n          ORDER BY timestamp ASC\n        `, [\n                    convRow.id\n                ]);\n                const messages = messagesResult.rows.map((msgRow)=>({\n                        id: msgRow.id,\n                        role: msgRow.role,\n                        content: msgRow.content,\n                        timestamp: msgRow.timestamp.getTime(),\n                        isStreaming: msgRow.is_streaming\n                    }));\n                conversations.push({\n                    id: convRow.id,\n                    title: convRow.title,\n                    messages,\n                    workspaceId: convRow.workspace_id,\n                    createdAt: convRow.created_at.getTime(),\n                    updatedAt: convRow.updated_at.getTime()\n                });\n            }\n            return conversations;\n        } finally{\n            client.release();\n        }\n    }\n    // Individual conversation operations\n    static async deleteConversation(deviceId, conversationId) {\n        const client = await this.getClient();\n        try {\n            await client.query('BEGIN');\n            const userId = await this.findOrCreateUser(deviceId);\n            // Verify the conversation belongs to this user\n            const convResult = await client.query('SELECT id FROM conversations WHERE id = $1 AND user_id = $2', [\n                conversationId,\n                userId\n            ]);\n            if (convResult.rows.length === 0) {\n                throw new Error('Conversation not found or access denied');\n            }\n            // Delete messages first (due to foreign key constraint)\n            await client.query('DELETE FROM messages WHERE conversation_id = $1', [\n                conversationId\n            ]);\n            // Delete the conversation\n            await client.query('DELETE FROM conversations WHERE id = $1', [\n                conversationId\n            ]);\n            // Clear current conversation if it's the one being deleted\n            await client.query('UPDATE user_current_conversation SET conversation_id = NULL WHERE user_id = $1 AND conversation_id = $2', [\n                userId,\n                conversationId\n            ]);\n            await client.query('COMMIT');\n        } catch (error) {\n            await client.query('ROLLBACK');\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    static async updateConversationTitle(deviceId, conversationId, newTitle) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            // Verify the conversation belongs to this user and update it\n            const result = await client.query(`\n        UPDATE conversations \n        SET title = $1, updated_at = CURRENT_TIMESTAMP \n        WHERE id = $2 AND user_id = $3\n        RETURNING id\n      `, [\n                newTitle,\n                conversationId,\n                userId\n            ]);\n            if (result.rows.length === 0) {\n                throw new Error('Conversation not found or access denied');\n            }\n        } finally{\n            client.release();\n        }\n    }\n    static async updateSingleConversation(deviceId, conversation) {\n        console.log('DatabaseService.updateSingleConversation called:', {\n            deviceId,\n            conversationId: conversation.id,\n            messageCount: conversation.messages.length\n        });\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            console.log('Found/created user:', userId);\n            // Use UPSERT for conversation to avoid deadlocks\n            await client.query(`\n        INSERT INTO conversations (id, user_id, workspace_id, title, created_at, updated_at)\n        VALUES ($1, $2, $3, $4, $5, $6)\n        ON CONFLICT (id) DO UPDATE SET\n          workspace_id = EXCLUDED.workspace_id,\n          title = EXCLUDED.title,\n          updated_at = EXCLUDED.updated_at\n      `, [\n                conversation.id,\n                userId,\n                conversation.workspaceId || null,\n                conversation.title,\n                new Date(conversation.createdAt),\n                new Date(conversation.updatedAt)\n            ]);\n            console.log('Upserted conversation');\n            // Use a more efficient approach for messages - delete and insert in a single transaction\n            // but with shorter transaction scope\n            await client.query('BEGIN');\n            try {\n                // Delete existing messages for this conversation\n                const deleteResult = await client.query('DELETE FROM messages WHERE conversation_id = $1', [\n                    conversation.id\n                ]);\n                console.log('Deleted existing messages:', deleteResult.rowCount);\n                // Batch insert messages if there are any\n                if (conversation.messages.length > 0) {\n                    const values = conversation.messages.map((message, index)=>{\n                        const baseIndex = index * 6;\n                        return `($${baseIndex + 1}, $${baseIndex + 2}, $${baseIndex + 3}, $${baseIndex + 4}, $${baseIndex + 5}, $${baseIndex + 6})`;\n                    }).join(', ');\n                    const params = conversation.messages.flatMap((message)=>[\n                            message.id,\n                            conversation.id,\n                            message.role,\n                            message.content,\n                            new Date(message.timestamp),\n                            message.isStreaming || false\n                        ]);\n                    await client.query(`\n            INSERT INTO messages (id, conversation_id, role, content, timestamp, is_streaming)\n            VALUES ${values}\n          `, params);\n                    console.log('Batch inserted messages:', conversation.messages.length);\n                }\n                await client.query('COMMIT');\n                console.log('Successfully updated conversation in database');\n            } catch (error) {\n                await client.query('ROLLBACK');\n                throw error;\n            }\n        } catch (error) {\n            console.error('DatabaseService.updateSingleConversation error:', error);\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    // Current conversation management\n    static async setCurrentConversation(deviceId, conversationId) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            await client.query(`\n        INSERT INTO user_current_conversation (user_id, conversation_id)\n        VALUES ($1, $2)\n        ON CONFLICT (user_id) DO UPDATE SET\n          conversation_id = EXCLUDED.conversation_id,\n          updated_at = CURRENT_TIMESTAMP\n      `, [\n                userId,\n                conversationId\n            ]);\n        } finally{\n            client.release();\n        }\n    }\n    static async getCurrentConversation(deviceId) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            const result = await client.query(`\n        SELECT conversation_id\n        FROM user_current_conversation\n        WHERE user_id = $1\n      `, [\n                userId\n            ]);\n            return result.rows.length > 0 ? result.rows[0].conversation_id : null;\n        } finally{\n            client.release();\n        }\n    }\n    // Settings management\n    static async saveUserSettings(deviceId, settingsKey, settingsValue) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            // For JSONB columns, PostgreSQL expects a JavaScript object, not a JSON string\n            // The pg driver will automatically handle the conversion to JSONB\n            if (typeof settingsValue !== 'object' || settingsValue === null) {\n                throw new Error(`Settings value must be an object, got ${typeof settingsValue}`);\n            }\n            await client.query(`\n        INSERT INTO user_settings (user_id, settings_key, settings_value)\n        VALUES ($1, $2, $3)\n        ON CONFLICT (user_id, settings_key) DO UPDATE SET\n          settings_value = EXCLUDED.settings_value,\n          updated_at = CURRENT_TIMESTAMP\n      `, [\n                userId,\n                settingsKey,\n                settingsValue\n            ]);\n        } finally{\n            client.release();\n        }\n    }\n    static async loadUserSettings(deviceId, settingsKey) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            const result = await client.query(`\n        SELECT settings_value\n        FROM user_settings\n        WHERE user_id = $1 AND settings_key = $2\n      `, [\n                userId,\n                settingsKey\n            ]);\n            if (result.rows.length > 0) {\n                const rawValue = result.rows[0].settings_value;\n                // Since the column is JSONB, PostgreSQL returns it as an object, not a string\n                // No need to JSON.parse() - it's already parsed by PostgreSQL\n                if (rawValue && typeof rawValue === 'object') {\n                    return rawValue;\n                } else {\n                    console.error(`Invalid settings data type for ${deviceId}:${settingsKey}:`, typeof rawValue);\n                    // Clear the corrupted data\n                    await client.query(`\n            DELETE FROM user_settings\n            WHERE user_id = $1 AND settings_key = $2\n          `, [\n                        userId,\n                        settingsKey\n                    ]);\n                    console.log(`Cleared corrupted settings data for ${deviceId}:${settingsKey}`);\n                    return null;\n                }\n            }\n            return null;\n        } finally{\n            client.release();\n        }\n    }\n    // Clear all data for a user\n    static async clearUserData(deviceId) {\n        const client = await this.getClient();\n        try {\n            await client.query('BEGIN');\n            const userResult = await client.query('SELECT id FROM users WHERE device_id = $1', [\n                deviceId\n            ]);\n            if (userResult.rows.length > 0) {\n                const userId = userResult.rows[0].id;\n                // Delete user data (cascading deletes will handle related records)\n                await client.query('DELETE FROM users WHERE id = $1', [\n                    userId\n                ]);\n            }\n            await client.query('COMMIT');\n        } catch (error) {\n            await client.query('ROLLBACK');\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    // Workspace management\n    static async loadWorkspaces(deviceId) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            const result = await client.query(`\n        SELECT id, name, description, color, position, created_at, updated_at\n        FROM workspaces\n        WHERE user_id = $1\n        ORDER BY position ASC, created_at ASC\n      `, [\n                userId\n            ]);\n            return result.rows.map((row)=>({\n                    id: row.id,\n                    name: row.name,\n                    description: row.description,\n                    color: row.color,\n                    position: row.position || 0,\n                    createdAt: row.created_at.getTime(),\n                    updatedAt: row.updated_at.getTime(),\n                    userId\n                }));\n        } finally{\n            client.release();\n        }\n    }\n    static async createWorkspace(deviceId, workspace) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            // Get the next position (max position + 1)\n            const positionResult = await client.query(`\n        SELECT COALESCE(MAX(position), 0) + 1 as next_position\n        FROM workspaces\n        WHERE user_id = $1\n      `, [\n                userId\n            ]);\n            const nextPosition = workspace.position ?? positionResult.rows[0].next_position;\n            const result = await client.query(`\n        INSERT INTO workspaces (user_id, name, description, color, position)\n        VALUES ($1, $2, $3, $4, $5)\n        RETURNING id, name, description, color, position, created_at, updated_at\n      `, [\n                userId,\n                workspace.name,\n                workspace.description,\n                workspace.color,\n                nextPosition\n            ]);\n            const row = result.rows[0];\n            return {\n                id: row.id,\n                name: row.name,\n                description: row.description,\n                color: row.color,\n                position: row.position,\n                createdAt: row.created_at.getTime(),\n                updatedAt: row.updated_at.getTime(),\n                userId\n            };\n        } finally{\n            client.release();\n        }\n    }\n    static async updateWorkspace(deviceId, workspace) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            await client.query(`\n        UPDATE workspaces\n        SET name = $3, description = $4, color = $5, position = $6, updated_at = CURRENT_TIMESTAMP\n        WHERE id = $1 AND user_id = $2\n      `, [\n                workspace.id,\n                userId,\n                workspace.name,\n                workspace.description,\n                workspace.color,\n                workspace.position\n            ]);\n        } finally{\n            client.release();\n        }\n    }\n    static async deleteWorkspace(deviceId, workspaceId) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            // Delete workspace (conversations will have workspace_id set to NULL due to ON DELETE SET NULL)\n            await client.query(`\n        DELETE FROM workspaces\n        WHERE id = $1 AND user_id = $2\n      `, [\n                workspaceId,\n                userId\n            ]);\n        } finally{\n            client.release();\n        }\n    }\n    static async reorderWorkspaces(deviceId, workspaceIds) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            // Update positions based on the new order\n            for(let i = 0; i < workspaceIds.length; i++){\n                await client.query(`\n          UPDATE workspaces\n          SET position = $3, updated_at = CURRENT_TIMESTAMP\n          WHERE id = $1 AND user_id = $2\n        `, [\n                    workspaceIds[i],\n                    userId,\n                    i + 1\n                ]);\n            }\n        } finally{\n            client.release();\n        }\n    }\n    static async assignConversationToWorkspace(deviceId, conversationId, workspaceId) {\n        const client = await this.getClient();\n        try {\n            const userId = await this.findOrCreateUser(deviceId);\n            console.log('DB: Assigning conversation', conversationId, 'to workspace', workspaceId, 'for user', userId);\n            const result = await client.query(`\n        UPDATE conversations\n        SET workspace_id = $3, updated_at = CURRENT_TIMESTAMP\n        WHERE id = $1 AND user_id = $2\n      `, [\n                conversationId,\n                userId,\n                workspaceId\n            ]);\n            console.log('DB: Update result:', result.rowCount, 'rows affected');\n        } finally{\n            client.release();\n        }\n    }\n    // Health check\n    static async healthCheck() {\n        try {\n            const client = await this.getClient();\n            try {\n                await client.query('SELECT 1');\n                return true;\n            } finally{\n                client.release();\n            }\n        } catch (error) {\n            console.error('Database health check failed:', error);\n            return false;\n        }\n    }\n    // Close pool (for graceful shutdown)\n    static async close() {\n        if (pool) {\n            await pool.end();\n            pool = null;\n        }\n    }\n}\n// Initialize database on module load\nif (true) {\n    DatabaseService.initialize().catch(console.error);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute&page=%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstorage%2Fconversations%2F%5Bid%5D%2Fworkspace%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDev%5CAI%5CChat%5Cchat11%5Cai-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();