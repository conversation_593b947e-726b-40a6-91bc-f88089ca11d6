'use client';

import { useState } from 'react';
import { processToolTags, processToolTagsWithCollapse } from '@/utils/toolTagParser';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

const sampleToolTagContent = `Thailand and Cambodia have recently experienced intense border clashes, resulting in significant casualties and displacement. The conflict escalated after a landmine explosion injured five Thai soldiers in the disputed border area of Chong An Ma, Nam Yuen District, Ubon Ratchathani.

<tool call>{"name": "mark_task_complete", "arguments": {"summary": "Provided a comprehensive summary of the latest news regarding Thailand and Cambodia, focusing on recent border clashes, casualties, displacement, ceasefire agreements, and ongoing diplomatic efforts. Information was gathered from multiple credible news sources including Al Jazeera, Euronews, CBS News, and Anadolu Agency."}, "final_message": "The latest situation between Thailand and Cambodia involves serious border conflicts, with casualties and mass displacement, but an immediate and unconditional ceasefire has been agreed upon following international mediation. Diplomatic talks are ongoing to ensure lasting peace. Let me know if you'd like updates on humanitarian efforts or historical context."}</tool call>

Task completed. The latest developments between Thailand and Cambodia have been summarized, focusing on border clashes, casualties, displacement, and the recent ceasefire agreement.`;

export default function ToolTagDemo() {
  const [useCollapse, setUseCollapse] = useState(true);
  const [customContent, setCustomContent] = useState(sampleToolTagContent);

  const processedContent = useCollapse 
    ? processToolTagsWithCollapse(customContent)
    : processToolTags(customContent);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-card border border-border rounded-lg p-6">
        <h1 className="text-2xl font-bold mb-4 text-foreground">Tool Tag Processing Demo</h1>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={useCollapse}
                onChange={(e) => setUseCollapse(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-foreground">Use collapsible format</span>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Test Content (edit to try different tool tags):
            </label>
            <textarea
              value={customContent}
              onChange={(e) => setCustomContent(e.target.value)}
              className="w-full h-40 p-3 border border-border rounded-lg bg-background text-foreground resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter content with <tool>...</tool> tags..."
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4 text-foreground">Raw Content</h2>
          <pre className="bg-muted p-4 rounded-lg text-sm text-foreground overflow-x-auto whitespace-pre-wrap">
            {customContent}
          </pre>
        </div>

        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4 text-foreground">Processed Output</h2>
          <div className="bg-muted p-4 rounded-lg">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
              components={{
                h1: ({ children }) => (
                  <h1 className="text-xl font-bold mb-3 text-foreground">{children}</h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-lg font-bold mb-2 text-foreground">{children}</h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-base font-semibold mb-2 text-foreground">{children}</h3>
                ),
                p: ({ children }) => (
                  <p className="mb-3 text-foreground leading-relaxed">{children}</p>
                ),
                code: ({ inline, children, ...props }) => {
                  if (inline) {
                    return (
                      <code className="bg-background px-1 py-0.5 rounded text-sm font-mono text-foreground" {...props}>
                        {children}
                      </code>
                    );
                  }
                  return (
                    <pre className="bg-background p-3 rounded overflow-x-auto">
                      <code className="font-mono text-sm text-foreground" {...props}>
                        {children}
                      </code>
                    </pre>
                  );
                },
                details: ({ children }) => (
                  <details className="border border-border rounded-lg p-3 mb-3 bg-background">
                    {children}
                  </details>
                ),
                summary: ({ children }) => (
                  <summary className="font-semibold cursor-pointer text-foreground hover:text-blue-600 transition-colors">
                    {children}
                  </summary>
                ),
              }}
            >
              {processedContent}
            </ReactMarkdown>
          </div>
        </div>
      </div>

      <div className="bg-card border border-border rounded-lg p-6">
        <h2 className="text-lg font-semibold mb-4 text-foreground">How it works</h2>
        <div className="space-y-2 text-sm text-muted-foreground">
          <p>• The function looks for <code>&lt;tool&gt;...&lt;/tool&gt;</code> tags in the message content</p>
          <p>• It parses the JSON content inside the tags</p>
          <p>• It formats the tool call information into readable markdown</p>
          <p>• With collapsible format, tool calls are wrapped in HTML details/summary elements</p>
          <p>• The processed content is then rendered by ReactMarkdown with proper styling</p>
        </div>
      </div>
    </div>
  );
}
