import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

function getDeviceIdFromRequest(request: NextRequest): string {
  const { searchParams } = new URL(request.url);
  const deviceId = searchParams.get('deviceId');
  if (!deviceId) {
    throw new Error('Device ID is required');
  }
  return deviceId;
}

// PUT /api/storage/workspaces/reorder - Reorder workspaces
export async function PUT(request: NextRequest) {
  try {
    const deviceId = getDeviceIdFromRequest(request);
    const { workspaceIds } = await request.json();

    if (!Array.isArray(workspaceIds)) {
      return NextResponse.json(
        { error: 'workspaceIds must be an array' },
        { status: 400 }
      );
    }

    await DatabaseService.reorderWorkspaces(deviceId, workspaceIds);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to reorder workspaces:', error);
    return NextResponse.json(
      { error: 'Failed to reorder workspaces' },
      { status: 500 }
    );
  }
}
