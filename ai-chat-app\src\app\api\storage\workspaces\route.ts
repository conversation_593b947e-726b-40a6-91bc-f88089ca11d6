import { NextRequest, NextResponse } from 'next/server';
import { Workspace } from '@/types/chat';
import { DatabaseService } from '@/lib/database';

function getDeviceIdFromRequest(request: NextRequest): string {
  const { searchParams } = new URL(request.url);
  const deviceId = searchParams.get('deviceId');
  if (!deviceId) {
    throw new Error('Device ID is required');
  }
  return deviceId;
}

// GET /api/storage/workspaces - Load workspaces
export async function GET(request: NextRequest) {
  try {
    const deviceId = getDeviceIdFromRequest(request);
    const workspaces = await DatabaseService.loadWorkspaces(deviceId);
    return NextResponse.json({
      workspaces,
      deviceId,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('Failed to load workspaces:', error);
    return NextResponse.json(
      { error: 'Failed to load workspaces' },
      { status: 500 }
    );
  }
}

// POST /api/storage/workspaces - Create a new workspace
export async function POST(request: NextRequest) {
  try {
    const workspaceData = await request.json();

    // Validate required fields
    if (!workspaceData.name) {
      return NextResponse.json(
        { error: 'Workspace name is required' },
        { status: 400 }
      );
    }

    // Get device ID from request body or query params
    const deviceId = workspaceData.deviceId || getDeviceIdFromRequest(request);

    const newWorkspace = await DatabaseService.createWorkspace(deviceId, workspaceData);
    return NextResponse.json({ workspace: newWorkspace });
  } catch (error) {
    console.error('Failed to create workspace:', error);
    return NextResponse.json(
      { error: 'Failed to create workspace' },
      { status: 500 }
    );
  }
}
