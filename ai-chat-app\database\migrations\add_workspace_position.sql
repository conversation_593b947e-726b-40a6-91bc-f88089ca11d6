-- Migration to add position field to workspaces for custom ordering
-- This allows users to reorder workspaces by dragging them up/down

-- Add position column to workspaces table if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'workspaces' AND column_name = 'position'
    ) THEN
        ALTER TABLE workspaces ADD COLUMN position INTEGER DEFAULT 0;
    END IF;
END $$;

-- Set initial positions based on creation order
UPDATE workspaces 
SET position = subquery.row_number 
FROM (
    SELECT id, ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at ASC) as row_number
    FROM workspaces
) AS subquery 
WHERE workspaces.id = subquery.id AND workspaces.position = 0;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_workspaces_position ON workspaces(user_id, position);

-- Verify the migration
SELECT 
    'Migration completed successfully' as status,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'workspaces' AND column_name = 'position') as position_column_exists;
