import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

// PUT /api/storage/conversations/[id]/workspace - Assign conversation to workspace
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { workspaceId } = await request.json();
    const { id: conversationId } = await params;
    const deviceId = request.nextUrl.searchParams.get('deviceId');

    if (!deviceId) {
      return NextResponse.json({ error: 'Device ID is required' }, { status: 400 });
    }

    console.log('API: Assigning conversation', conversationId, 'to workspace', workspaceId, 'for device', deviceId);
    const result = await DatabaseService.assignConversationToWorkspace(deviceId, conversationId, workspaceId);
    console.log('API: Assignment result:', result);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to assign conversation to workspace:', error);
    return NextResponse.json(
      { error: 'Failed to assign conversation to workspace' },
      { status: 500 }
    );
  }
}
