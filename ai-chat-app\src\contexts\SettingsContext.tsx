'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { settingsService } from '@/lib/settings-service';

// Types for settings
export interface UserSettings {
  // UI Preferences
  theme: 'light' | 'dark' | 'auto';
  defaultMode: 'simple' | 'enhanced' | 'orchestrator';
  autoSave: boolean;
  
  // Model Preferences
  preferredModels: {
    simple: string;
    enhanced: string;
    orchestrator: string;
    refine: string;
    memorySummarization: string;
    titleGeneration: string;
  };
  
  // Provider Configuration
  providerSettings: {
    baseUrl: string;
    apiKey: string;
    availableModels: string[];
  };
  
  // Agent Configuration
  agentSettings: {
    maxIterations: number;
    temperature: number;
    enableTools: string[];
  };

  // Orchestrator Configuration
  orchestratorSettings: {
    parallelAgents: number;
    taskTimeout: number;
    aggregationStrategy: 'consensus' | 'majority' | 'best';
    enableCollaboration: boolean;
  };

  // Response Generation Limits
  responseSettings: {
    maxTokens: {
      simple: number;
      enhanced: number;
      orchestrator: number;
    };
    streaming: {
      simple: number;
      enhanced: number;
      orchestrator: number;
    };
    temperature: number;
  };
  
  // Zep Memory Configuration
  zepSettings: {
    enabled: boolean;
    apiUrl: string;
    apiKey: string;
    autoMemoryExtraction: boolean;
    memoryRetentionDays: number;
    showMemoryInsights: boolean;
    minFactRating: number;
  };
}

// Get default settings with environment variable fallbacks
function getDefaultSettings(): UserSettings {
  return {
    theme: 'auto',
    defaultMode: 'simple',
    autoSave: true,
    preferredModels: {
      simple: 'Qwen/Qwen3-235B-A22B-Instruct-2507',
      enhanced: 'Qwen/Qwen3-235B-A22B-Instruct-2507',
      orchestrator: 'Qwen/Qwen3-235B-A22B-Instruct-2507',
      refine: 'Qwen/Qwen3-235B-A22B-Instruct-2507',
      memorySummarization: 'Qwen/Qwen3-235B-A22B-Instruct-2507',
      titleGeneration: 'Qwen/Qwen3-235B-A22B-Instruct-2507'
    },
    providerSettings: {
      baseUrl: process.env.NEXT_PUBLIC_CHUTES_API_URL || 'https://llm.chutes.ai/v1',
      apiKey: process.env.NEXT_PUBLIC_CHUTES_API_TOKEN || '', // Always from env
      availableModels: []
    },
    agentSettings: {
      maxIterations: 5,
      temperature: 0.7,
      enableTools: ['web_search', 'calculator', 'file_operations']
    },
    orchestratorSettings: {
      parallelAgents: 3,
      taskTimeout: 300,
      aggregationStrategy: 'consensus',
      enableCollaboration: true
    },
    responseSettings: {
      maxTokens: {
        simple: 8192,        // Increased from hardcoded 4000
        enhanced: 16384,     // Higher limit for enhanced mode
        orchestrator: 32768  // Highest limit for complex orchestrator responses
      },
      streaming: {
        simple: 8192,        // Increased from hardcoded 1024
        enhanced: 16384,     // Consistent with non-streaming
        orchestrator: 32768  // Consistent with non-streaming
      },
      temperature: 0.7
    },
    zepSettings: {
      enabled: true,
      apiUrl: process.env.NEXT_PUBLIC_ZEP_API_URL || 'http://localhost:8001',
      apiKey: process.env.NEXT_PUBLIC_ZEP_API_KEY || 'your-local-zep-secret-key', // Always from env
      autoMemoryExtraction: true,
      memoryRetentionDays: 30,
      showMemoryInsights: true,
      minFactRating: 7
    }
  };
}

// Context type
interface SettingsContextType {
  settings: UserSettings;
  updateSettings: (updates: Partial<UserSettings>) => Promise<void>;
  resetSettings: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

// Create context
const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

// Settings provider component
export const SettingsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [settings, setSettings] = useState<UserSettings>(getDefaultSettings());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, []);

  // Apply theme to document
  useEffect(() => {
    const applyTheme = () => {
      const root = document.documentElement;
      
      // Remove existing theme classes
      root.classList.remove('light', 'dark');
      
      if (settings.theme === 'auto') {
        // Use system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        root.classList.add(prefersDark ? 'dark' : 'light');
      } else {
        // Use explicit theme
        root.classList.add(settings.theme);
      }
    };

    applyTheme();

    // Listen for system theme changes when in auto mode
    if (settings.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => applyTheme();
      mediaQuery.addEventListener('change', handleChange);
      
      return () => {
        mediaQuery.removeEventListener('change', handleChange);
      };
    }
  }, [settings.theme]);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const defaults = getDefaultSettings();

      // Load from API using SettingsService
      const apiSettings = await settingsService.getSettings();
      const mergedSettings = {
        ...defaults,
        ...apiSettings,
        preferredModels: {
          ...defaults.preferredModels,
          ...apiSettings.preferredModels,
        },
        providerSettings: {
          ...defaults.providerSettings,
          ...apiSettings.providerSettings,
          // Always override API key with environment variable
          apiKey: process.env.NEXT_PUBLIC_CHUTES_API_TOKEN || '',
        },
        agentSettings: {
          ...defaults.agentSettings,
          ...apiSettings.agentSettings,
        },
        orchestratorSettings: {
          ...defaults.orchestratorSettings,
          ...apiSettings.orchestratorSettings,
        },
        zepSettings: {
          ...defaults.zepSettings,
          ...apiSettings.zepSettings,
          // Always override API key with environment variable
          apiKey: process.env.NEXT_PUBLIC_ZEP_API_KEY || 'your-local-zep-secret-key',
        },
      };
      setSettings(mergedSettings);
    } catch (err) {
      console.error('Failed to load settings:', err);
      setError('Failed to load settings');
      setSettings(getDefaultSettings());
    } finally {
      setIsLoading(false);
    }
  };

  const updateSettings = async (updates: Partial<UserSettings>) => {
    try {
      setError(null);

      // Deep merge updates with current settings
      const newSettings = {
        ...settings,
        ...updates,
        preferredModels: {
          ...settings.preferredModels,
          ...updates.preferredModels,
        },
        providerSettings: {
          ...settings.providerSettings,
          ...updates.providerSettings,
          // Always keep API key from environment
          apiKey: process.env.NEXT_PUBLIC_CHUTES_API_TOKEN || '',
        },
        agentSettings: {
          ...settings.agentSettings,
          ...updates.agentSettings,
        },
        orchestratorSettings: {
          ...settings.orchestratorSettings,
          ...updates.orchestratorSettings,
        },
        zepSettings: {
          ...settings.zepSettings,
          ...updates.zepSettings,
          // Always keep API key from environment
          apiKey: process.env.NEXT_PUBLIC_ZEP_API_KEY || 'your-local-zep-secret-key',
        },
      };

      // Filter out sensitive data before saving to database
      const settingsToSave = {
        ...newSettings,
        providerSettings: {
          ...newSettings.providerSettings,
          apiKey: '', // Never save API keys
        },
        zepSettings: {
          ...newSettings.zepSettings,
          apiKey: '', // Never save API keys
        },
      };

      // Update local state immediately for responsive UI (with environment variables)
      setSettings(newSettings);

      // Save to API (without sensitive data)
      await settingsService.updateSettings(settingsToSave);
    } catch (err) {
      console.error('Failed to update settings:', err);
      setError('Failed to update settings');
      throw err;
    }
  };

  const resetSettings = async () => {
    try {
      setError(null);
      setSettings(getDefaultSettings());

      // Reset on API
      await settingsService.resetSettings();
    } catch (err) {
      console.error('Failed to reset settings:', err);
      setError('Failed to reset settings');
      throw err;
    }
  };

  const value: SettingsContextType = {
    settings,
    updateSettings,
    resetSettings,
    isLoading,
    error
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

// Hook to use settings context
export const useSettings = (): SettingsContextType => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

export default SettingsContext;