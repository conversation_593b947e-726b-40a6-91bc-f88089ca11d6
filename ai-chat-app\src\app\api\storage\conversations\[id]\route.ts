import { NextRequest, NextResponse } from 'next/server';
import { Conversation } from '@/types/chat';

// Dynamic import to avoid potential circular dependency issues
async function getDatabaseService() {
  const { DatabaseService } = await import('@/lib/database');
  return DatabaseService;
}

// DELETE - Delete a specific conversation
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const deviceId = searchParams.get('deviceId');
    const resolvedParams = await params;
    const conversationId = resolvedParams.id;

    if (!deviceId) {
      return NextResponse.json(
        { error: 'Device ID is required' },
        { status: 400 }
      );
    }

    if (!conversationId) {
      return NextResponse.json(
        { error: 'Conversation ID is required' },
        { status: 400 }
      );
    }

    const DatabaseService = await getDatabaseService();
    await DatabaseService.deleteConversation(deviceId, conversationId);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to delete conversation:', error);
    return NextResponse.json(
      { error: 'Failed to delete conversation' },
      { status: 500 }
    );
  }
}

// PUT - Update a specific conversation
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const deviceId = searchParams.get('deviceId');
    const resolvedParams = await params;
    const conversationId = resolvedParams.id;
    const body = await request.json();

    if (!deviceId) {
      return NextResponse.json(
        { error: 'Device ID is required' },
        { status: 400 }
      );
    }

    if (!conversationId) {
      return NextResponse.json(
        { error: 'Conversation ID is required' },
        { status: 400 }
      );
    }

    // Handle title-only updates
    if (body.title && typeof body.title === 'string') {
      const DatabaseService = await getDatabaseService();
      await DatabaseService.updateConversationTitle(deviceId, conversationId, body.title);
      return NextResponse.json({ success: true });
    }

    // Handle full conversation updates
    if (body.conversation && typeof body.conversation === 'object') {
      const conversation = body.conversation as Conversation;

      if (conversation.id !== conversationId) {
        return NextResponse.json(
          { error: 'Conversation ID mismatch' },
          { status: 400 }
        );
      }

      console.log('About to call DatabaseService.updateSingleConversation with:', {
        deviceId,
        conversationId: conversation.id
      });

      const DatabaseService = await getDatabaseService();
      await DatabaseService.updateSingleConversation(deviceId, conversation);
      return NextResponse.json({ success: true });
    }

    return NextResponse.json(
      { error: 'Invalid request body. Expected title or conversation object.' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Failed to update conversation:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      type: typeof error,
      error
    });
    return NextResponse.json(
      { error: 'Failed to update conversation', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}