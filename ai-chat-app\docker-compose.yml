version: '3.8'

services:
  # PostgreSQL Database
  nexus-db:
    image: postgres:15-alpine
    container_name: nexus-db
    environment:
      POSTGRES_DB: ai_chat_app
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"
    volumes:
      - nexus_db_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/migrations:/docker-entrypoint-initdb.d/migrations
    networks:
      - nexus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ai_chat_app"]
      interval: 10s
      timeout: 5s
      retries: 5

  # AI Chat Application (Frontend + Backend)
  nexus-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nexus-app
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/ai_chat_app
      - NEXT_PUBLIC_API_URL=http://localhost:3000
    ports:
      - "3000:3000"
    depends_on:
      nexus-db:
        condition: service_healthy
    networks:
      - nexus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Python AI Service (if needed)
  nexus-ai-service:
    image: python:3.11-slim
    container_name: nexus-ai-service
    working_dir: /app
    command: >
      sh -c "
        pip install fastapi uvicorn openai python-dotenv &&
        echo 'from fastapi import FastAPI
        app = FastAPI()
        @app.get(\"/\")
        def read_root():
            return {\"status\": \"healthy\", \"service\": \"nexus-ai\"}
        @app.get(\"/health\")
        def health_check():
            return {\"status\": \"healthy\"}' > main.py &&
        uvicorn main:app --host 0.0.0.0 --port 8000
      "
    ports:
      - "8000:8000"
    networks:
      - nexus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  nexus_db_data:
    driver: local

networks:
  nexus-network:
    driver: bridge
