import { Conversation, Workspace } from '@/types/chat';
import { getDeviceId, migrateExistingUsers } from '@/lib/device-id';

// Conditionally import DatabaseService only on server side
let DatabaseService: any = null;
if (typeof window === 'undefined') {
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    DatabaseService = require('@/lib/database').DatabaseService;
  } catch (error) {
    console.error('DatabaseService not available:', error);
    throw new Error('Database service is required for storage operations');
  }
}

// Client-side API helpers
const isClientSide = () => typeof window !== 'undefined';

const apiCall = async (url: string, options?: RequestInit, retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log('Making API call:', { url, method: options?.method || 'GET', attempt });
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        let errorText = 'Unknown error';
        let errorDetails = {};

        try {
          errorText = await response.text();
          // Try to parse as JSON for more details
          try {
            errorDetails = JSON.parse(errorText);
          } catch {
            // If not JSON, use the text as is
            errorDetails = { message: errorText };
          }
        } catch (textError) {
          console.warn('Failed to read error response:', textError);
        }

        const errorInfo = {
          url,
          status: response.status,
          statusText: response.statusText,
          errorText,
          errorDetails,
          attempt
        };

        console.error('API call failed:', errorInfo);

        // Check if it's a server error that might be retryable
        if (response.status >= 500 && attempt < retries) {
          console.log(`Retrying API call in ${attempt * 1000}ms...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 1000));
          continue;
        }

        const errorMessage = `API call failed: ${response.status} ${response.statusText}${errorText ? ` - ${errorText}` : ''}`;
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log('API call successful:', { url, method: options?.method || 'GET', attempt });
      return result;
    } catch (error) {
      console.error('API call error:', {
        url,
        method: options?.method || 'GET',
        attempt,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });

      // If it's the last attempt or not a retryable error, throw
      if (attempt === retries || !(error instanceof Error) || !error.message.includes('deadlock')) {
        throw error;
      }

      // Wait before retrying
      console.log(`Retrying API call in ${attempt * 1000}ms due to error...`);
      await new Promise(resolve => setTimeout(resolve, attempt * 1000));
    }
  }
};

// Device ID is now handled by the unified device-id service
// This function is kept for backwards compatibility but now uses the new service

export class StorageService {
  // Database-only storage - no localStorage fallbacks
  
  static async saveConversations(conversations: Conversation[]): Promise<void> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      await apiCall('/api/storage/conversations', {
        method: 'POST',
        body: JSON.stringify({ deviceId, conversations }),
      });
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.saveConversations(deviceId, conversations);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async loadConversations(): Promise<Conversation[]> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      const response = await apiCall(`/api/storage/conversations?deviceId=${encodeURIComponent(deviceId)}`);
      const conversations = response.conversations || [];
      return conversations.sort((a, b) => b.updatedAt - a.updatedAt);
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      const conversations = await DatabaseService.loadConversations(deviceId);
      return conversations.sort((a, b) => b.updatedAt - a.updatedAt);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async saveCurrentConversationId(id: string | null): Promise<void> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      await apiCall('/api/storage/current-conversation', {
        method: 'POST',
        body: JSON.stringify({ deviceId, conversationId: id }),
      });
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.setCurrentConversation(deviceId, id);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async loadCurrentConversationId(): Promise<string | null> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      const response = await apiCall(`/api/storage/current-conversation?deviceId=${encodeURIComponent(deviceId)}`);
      return response.conversationId || null;
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      return await DatabaseService.getCurrentConversation(deviceId);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async deleteConversation(id: string): Promise<void> {
    try {
      if (isClientSide()) {
        // Use API endpoint for client-side
        const deviceId = getDeviceId();
        await apiCall(`/api/storage/conversations/${id}?deviceId=${deviceId}`, {
          method: 'DELETE'
        });
        
        // Clear current conversation if it's the one being deleted
        const currentId = await this.loadCurrentConversationId();
        if (currentId === id) {
          await this.saveCurrentConversationId(null);
        }
      } else {
        // Server-side: use DatabaseService directly
        if (DatabaseService) {
          const deviceId = getDeviceId();
          await DatabaseService.deleteConversation(deviceId, id);
        } else {
          // Fallback to local storage manipulation
          const conversations = await this.loadConversations();
          const filtered = conversations.filter(conv => conv.id !== id);
          await this.saveConversations(filtered);
          
          const currentId = await this.loadCurrentConversationId();
          if (currentId === id) {
            await this.saveCurrentConversationId(null);
          }
        }
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      throw error;
    }
  }

  static async updateConversation(conversation: Conversation): Promise<void> {
    try {
      console.log('StorageService.updateConversation called:', {
        conversationId: conversation.id,
        messageCount: conversation.messages.length,
        isClientSide: isClientSide()
      });

      if (isClientSide()) {
        // Check if conversation exists first
        const conversations = await this.loadConversations();
        const existingConversation = conversations.find(conv => conv.id === conversation.id);

        if (existingConversation) {
          // Use PUT endpoint for existing conversations with retry
          const deviceId = getDeviceId();
          console.log('Updating existing conversation via PUT endpoint');
          await apiCall(`/api/storage/conversations/${conversation.id}?deviceId=${deviceId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ conversation: { ...conversation, updatedAt: Date.now() } })
          }, 3); // 3 retries for database operations
          console.log('Successfully updated existing conversation');
        } else {
          // For new conversations, add to existing conversations and save all
          console.log('Creating new conversation via POST endpoint');
          const updatedConversations = [{ ...conversation, updatedAt: Date.now() }, ...conversations];
          await this.saveConversations(updatedConversations);
          console.log('Successfully created new conversation');
        }
      } else if (DatabaseService) {
        // Server-side: use DatabaseService directly
        const deviceId = getDeviceId();
        console.log('Updating conversation via DatabaseService');
        await DatabaseService.updateSingleConversation(deviceId, { ...conversation, updatedAt: Date.now() });
        console.log('Successfully updated conversation via DatabaseService');
      } else {
        throw new Error('Database service not available');
      }
    } catch (error) {
      console.error('StorageService.updateConversation failed:', error);
      throw error;
    }
  }

  static async renameConversation(id: string, newTitle: string): Promise<void> {
    if (isClientSide()) {
      // Use API endpoint for client-side
      const deviceId = getDeviceId();
      await apiCall(`/api/storage/conversations/${id}?deviceId=${deviceId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ title: newTitle })
      });
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.updateConversationTitle(deviceId, id, newTitle);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async deleteMessage(conversationId: string, messageId: string): Promise<void> {
    try {
      if (isClientSide()) {
        // Use API endpoint for client-side
        const deviceId = getDeviceId();
        await apiCall(`/api/storage/conversations/${conversationId}/messages/${messageId}?deviceId=${deviceId}`, {
          method: 'DELETE'
        });
      } else {
        // Server-side: use DatabaseService directly
        if (DatabaseService) {
          const deviceId = getDeviceId();
          await DatabaseService.deleteMessage(deviceId, conversationId, messageId);
        } else {
          throw new Error('Database service not available');
        }
      }
    } catch (error) {
      console.error('Failed to delete message:', error);
      throw error;
    }
  }

  static async clearAllData(): Promise<void> {
    if (isClientSide()) {
      // Use API endpoint to clear data
      const deviceId = getDeviceId();
      await apiCall('/api/storage/clear', {
        method: 'DELETE',
        body: JSON.stringify({ deviceId })
      });
      // Clear device ID using the new service
      const { clearDeviceId } = await import('@/lib/device-id');
      clearDeviceId();
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.clearUserData(deviceId);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async initialize(): Promise<void> {
    if (DatabaseService) {
      // Initialize database service
      await DatabaseService.initialize();
      console.log('Storage service initialized with database support');
    } else if (isClientSide()) {
      // Initialize device ID migration on client-side
      await migrateExistingUsers();
      console.log('Storage service initialized for client-side API calls');
    } else {
      throw new Error('Database service not available on server-side');
    }
  }

  // Utility methods for compatibility
  static async getStorageInfo(): Promise<{ version: string; conversationCount: number; hasCurrentConversation: boolean }> {
    try {
      const conversations = await this.loadConversations();
      const currentId = await this.loadCurrentConversationId();
      
      return {
        version: 'database',
        conversationCount: conversations.length,
        hasCurrentConversation: !!currentId
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return {
        version: 'unknown',
        conversationCount: 0,
        hasCurrentConversation: false
      };
    }
  }

  // Workspace management
  static async loadWorkspaces(): Promise<Workspace[]> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      const response = await apiCall(`/api/storage/workspaces?deviceId=${encodeURIComponent(deviceId)}`);
      const workspaces = response.workspaces || [];
      // Database already returns workspaces sorted by position, don't re-sort
      return workspaces;
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      const workspaces = await DatabaseService.loadWorkspaces(deviceId);
      // Database already returns workspaces sorted by position, don't re-sort
      return workspaces;
    } else {
      throw new Error('Database service not available');
    }
  }

  static async createWorkspace(workspace: Omit<Workspace, 'id' | 'createdAt' | 'updatedAt' | 'userId'>): Promise<Workspace> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      const response = await apiCall('/api/storage/workspaces', {
        method: 'POST',
        body: JSON.stringify({ ...workspace, deviceId }),
      });
      return response.workspace;
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      return await DatabaseService.createWorkspace(deviceId, workspace);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async updateWorkspace(workspace: Workspace): Promise<void> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      await apiCall(`/api/storage/workspaces/${workspace.id}?deviceId=${deviceId}`, {
        method: 'PUT',
        body: JSON.stringify({ workspace }),
      });
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.updateWorkspace(deviceId, workspace);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async deleteWorkspace(workspaceId: string): Promise<void> {
    console.log('StorageService.deleteWorkspace called with:', workspaceId);
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      console.log('Using client-side API call with deviceId:', deviceId);
      const url = `/api/storage/workspaces/${workspaceId}?deviceId=${deviceId}`;
      console.log('DELETE request URL:', url);
      await apiCall(url, {
        method: 'DELETE',
      });
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      console.log('Using server-side DatabaseService with deviceId:', deviceId);
      await DatabaseService.deleteWorkspace(deviceId, workspaceId);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async reorderWorkspaces(workspaceIds: string[]): Promise<void> {
    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      await apiCall(`/api/storage/workspaces/reorder?deviceId=${deviceId}`, {
        method: 'PUT',
        body: JSON.stringify({ workspaceIds }),
      });
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.reorderWorkspaces(deviceId, workspaceIds);
    } else {
      throw new Error('Database service not available');
    }
  }

  static async assignConversationToWorkspace(conversationId: string, workspaceId: string | null): Promise<void> {
    // Validate inputs to prevent circular JSON structure errors
    if (typeof conversationId !== 'string') {
      throw new Error(`Invalid conversationId: expected string, got ${typeof conversationId}`);
    }
    if (workspaceId !== null && typeof workspaceId !== 'string') {
      throw new Error(`Invalid workspaceId: expected string or null, got ${typeof workspaceId}`);
    }

    console.log('assignConversationToWorkspace called with:', { conversationId, workspaceId, workspaceIdType: typeof workspaceId });

    if (isClientSide()) {
      // Client-side: use API endpoint
      const deviceId = getDeviceId();
      await apiCall(`/api/storage/conversations/${conversationId}/workspace?deviceId=${deviceId}`, {
        method: 'PUT',
        body: JSON.stringify({ workspaceId }),
      });
    } else if (DatabaseService) {
      // Server-side: use DatabaseService directly
      const deviceId = getDeviceId();
      await DatabaseService.assignConversationToWorkspace(deviceId, conversationId, workspaceId);
    } else {
      throw new Error('Database service not available');
    }
  }
}
