'use client';

import { useState, useEffect } from 'react';
import { CpuChipIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

interface ProgressIndicatorProps {
  isVisible: boolean;
  currentStatus?: string;
  mode?: 'enhanced' | 'orchestrator';
}

export default function ProgressIndicator({ 
  isVisible, 
  currentStatus, 
  mode = 'enhanced' 
}: ProgressIndicatorProps) {
  const [dots, setDots] = useState('');

  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, [isVisible]);

  if (!isVisible) return null;

  const getStatusIcon = (status: string) => {
    if (status.includes('🚀') || status.includes('Starting')) return '🚀';
    if (status.includes('🧠') || status.includes('Breaking')) return '🧠';
    if (status.includes('⚡') || status.includes('Launching')) return '⚡';
    if (status.includes('🔄') || status.includes('Synthesizing')) return '🔄';
    if (status.includes('🔍') || status.includes('search')) return '🔍';
    if (status.includes('🧮') || status.includes('calculat')) return '🧮';
    if (status.includes('📁') || status.includes('file')) return '📁';
    return '⚙️';
  };

  const getModeConfig = () => {
    if (mode === 'orchestrator') {
      return {
        title: 'Research Mode Active',
        subtitle: 'Multi-agent analysis in progress',
        color: 'purple',
        bgColor: 'bg-purple-50',
        borderColor: 'border-purple-200',
        textColor: 'text-purple-800',
        iconColor: 'text-purple-600'
      };
    }
    
    return {
      title: 'Enhanced Mode Active',
      subtitle: 'AI tools and capabilities enabled',
      color: 'blue',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-800',
      iconColor: 'text-blue-600'
    };
  };

  const config = getModeConfig();

  return (
    <div className={cn(
      "group flex gap-4 p-4 relative bg-muted/30"
    )}>
      {/* Avatar - matching MessageBubble style with proper alignment */}
      <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-green-600 text-white mt-0">
        <CpuChipIcon className="w-5 h-5" />
      </div>

      {/* Message Content - matching MessageBubble structure */}
      <div className="flex-1 min-w-0">
        {/* Role Label - matching MessageBubble */}
        <div className="text-sm font-medium text-muted-foreground mb-1">
          Nexus
        </div>

        {/* Progress Content - matching MessageBubble structure exactly */}
        <div className="text-foreground">
          <div className="flex items-center space-x-2 mb-2">
            {/* Animated spinner */}
            <div className={`
              w-4 h-4 border-2 border-${config.color}-200 border-t-${config.color}-600
              rounded-full animate-spin
            `}></div>

            <span className="text-sm font-medium text-foreground">
              {config.title}
            </span>
            <span className="text-xs text-muted-foreground">
              {dots}
            </span>
          </div>

          <p className="text-xs text-muted-foreground mb-2">
            {config.subtitle}
          </p>

          {currentStatus && (
            <div className="flex items-center space-x-2">
              <span className="text-sm">
                {getStatusIcon(currentStatus)}
              </span>
              <span className="text-sm text-foreground">
                {currentStatus}
              </span>
            </div>
          )}

          {mode === 'orchestrator' && !currentStatus && (
            <div className="space-y-1 mt-2">
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <span>Analyzing query complexity</span>
              </div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse delay-100"></div>
                <span>Deploying specialized agents</span>
              </div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <div className="w-2 h-2 bg-purple-200 rounded-full animate-pulse delay-200"></div>
                <span>Gathering comprehensive insights</span>
              </div>
            </div>
          )}

          {mode === 'enhanced' && !currentStatus && (
            <div className="space-y-1 mt-2">
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                <span>Processing with AI tools</span>
              </div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse delay-100"></div>
                <span>Accessing real-time information</span>
              </div>
            </div>
          )}

          {/* Progress bar for orchestrator mode */}
          {mode === 'orchestrator' && (
            <div className="mt-3">
              <div className="w-full bg-purple-200 rounded-full h-1.5">
                <div className="bg-purple-600 h-1.5 rounded-full animate-pulse"
                     style={{ width: '60%' }}></div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
